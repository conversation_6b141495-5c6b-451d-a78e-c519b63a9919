(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-35997112"],{"2f8d":function(t,e,a){},"33b6":function(t,e,a){},3963:function(t,e,a){},"3c62":function(t,e,a){"use strict";a("7103")},"49e5":function(t,e,a){"use strict";a("2f8d")},5068:function(t,e,a){"use strict";a("3963")},"6fd3":function(t,e,a){"use strict";a("498a"),a("a434"),a("14d9"),a("13d5"),a("d3b7");var i=a("c4c8"),o={name:"addAttr",data:function(){return{spinShow:!1,modal_loading:!1,grid:{xl:3,lg:3,md:12,sm:24,xs:24},modal:!1,index:1,rules:{rule_name:[{required:!0,message:"请输入规格名称",trigger:"blur"}]},formDynamic:{rule_name:"",spec:[]},attrsName:"",attrsVal:"",formDynamicNameData:[],isBtn:!1,formDynamicName:[],results:[],result:[],ids:0}},computed:{},methods:{onCancel:function(){this.ids=0,this.clear()},onClose:function(){this.ids=0,this.clear(),this.modal=!1},addBtn:function(){this.isBtn=!0},getIofo:function(t){var e=this;this.spinShow=!0,this.ids=t.id,Object(i.C)(t.id).then((function(t){e.formDynamic=t.data.info,e.spinShow=!1})).catch((function(t){e.spinShow=!1,e.$message.error(t.msg)}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){return!!t&&(0===e.formDynamic.spec.length?e.$message.warning("请至少添加一条商品规格！"):(e.modal_loading=!0,void setTimeout((function(){Object(i.B)(e.formDynamic,e.ids).then((function(t){e.$message.success(t.msg),setTimeout((function(){e.modal=!1,e.modal_loading=!1}),500),setTimeout((function(){e.$emit("getList"),e.clear()}),600)})).catch((function(t){e.modal_loading=!1,e.$message.error(t.msg)}))}),1200)))}))},clear:function(){this.$refs.formDynamic.resetFields(),this.formDynamic.spec=[],this.isBtn=!1,this.attrsName="",this.attrsVal="",this.ids=0},offAttrName:function(){this.isBtn=!1},handleRemove:function(t){this.formDynamic.spec.splice(t,1)},handleRemove2:function(t,e){t.splice(e,1)},createAttrName:function(){var t,e;this.attrsName&&this.attrsVal?(t={value:this.attrsName,detail:[this.attrsVal]},this.formDynamic.spec.push(t),e={},this.formDynamic.spec=this.formDynamic.spec.reduce((function(t,a){return e[a.value]||(e[a.value]=t.push(a)),t}),[]),this.attrsName="",this.attrsVal="",this.isBtn=!1):this.$message.warning("请添加规格名称或规格值")},createAttr:function(t,e){var a;t?(this.formDynamic.spec[e].detail.push(t),a={},this.formDynamic.spec[e].detail=this.formDynamic.spec[e].detail.reduce((function(t,e){return a[e]||(a[e]=t.push(e)),t}),[])):this.$message.warning("请添加属性")}}};a("71d8"),a=a("2877"),a=Object(a.a)(o,(function(){var t=this,e=t._self._c;return e("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.spinShow,expression:"spinShow"}],attrs:{visible:t.modal,title:"商品规格",width:"1000px"},on:{"update:visible":function(e){t.modal=e},closed:t.onCancel}},[e("el-form",{ref:"formDynamic",staticClass:"attrFrom",attrs:{model:t.formDynamic,rules:t.rules,"label-width":"110px","label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-row",{attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"规格模板名称：",prop:"rule_name"}},[e("el-input",{attrs:{placeholder:"请输入标题名称",maxlength:20},model:{value:t.formDynamic.rule_name,callback:function(e){t.$set(t.formDynamic,"rule_name","string"==typeof e?e.trim():e)},expression:"formDynamic.rule_name"}})],1)],1)],1),t._l(t.formDynamic.spec,(function(a,i){return e("el-col",{key:i,staticClass:"noForm",attrs:{span:23}},[e("el-form-item",[e("div",{staticClass:"acea-row row-middle"},[e("span",{staticClass:"mr5"},[t._v(t._s(a.value))]),e("i",{staticClass:"el-icon-close",staticStyle:{"font-size":"14px"},on:{click:function(e){return t.handleRemove(i)}}})]),e("div",{staticClass:"rulesBox"},[t._l(a.detail,(function(i,o){return e("el-tag",{key:o,staticClass:"mr14 mb10",attrs:{closable:"",color:"primary"},on:{close:function(e){return t.handleRemove2(a.detail,o)}}},[t._v(t._s(i))])})),e("el-input",{staticClass:"mb10 form_content_width",attrs:{placeholder:"请输入属性名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.createAttr(a.detail.attrsVal,i)}},model:{value:a.detail.attrsVal,callback:function(e){t.$set(a.detail,"attrsVal","string"==typeof e?e.trim():e)},expression:"item.detail.attrsVal"}},[e("template",{slot:"append"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createAttr(a.detail.attrsVal,i)}}},[t._v("确定")])],1)],2)],2)])],1)})),t.isBtn?e("el-col",{staticClass:"mt10",attrs:{span:24}},[e("el-col",{staticClass:"mr15",attrs:{span:8}},[e("el-form-item",{attrs:{label:"规格名称："}},[e("el-input",{attrs:{placeholder:"请输入规格"},model:{value:t.attrsName,callback:function(e){t.attrsName=e},expression:"attrsName"}})],1)],1),e("el-col",{staticClass:"mr20",attrs:{span:8}},[e("el-form-item",{attrs:{label:"规格值："}},[e("el-input",{attrs:{placeholder:"请输入规格值"},model:{value:t.attrsVal,callback:function(e){t.attrsVal=e},expression:"attrsVal"}})],1)],1),e("el-col",{attrs:{span:2}},[e("el-button",{attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")])],1),e("el-col",{attrs:{span:2}},[e("el-button",{on:{click:t.offAttrName}},[t._v("取消")])],1)],1):t._e()],2),t.isBtn?t._e():e("el-button",{staticClass:"add",attrs:{type:"primary"},on:{click:t.addBtn}},[t._v("添加新规格")])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.onClose}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formDynamic")}}},[t._v("确定")])],1)],1)}),[],!1,null,"0fec9316",null);e.a=a.exports},7103:function(t,e,a){},"71d8":function(t,e,a){"use strict";a("33b6")},c7e8:function(t,e,a){"use strict";a.r(e),a("b0c0"),a("498a"),a("caad"),a("4e82");var i=a("ade3"),o=a("2909"),s=a("c7eb"),l=a("1da1"),r=a("06c5");function n(t,e){var a,i,o,s,l="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(l)return i=!(a=!0),{s:function(){l=l.call(t)},n:function(){var t=l.next();return a=t.done,t},e:function(t){i=!0,o=t},f:function(){try{a||null==l.return||l.return()}finally{if(i)throw o}}};if(Array.isArray(t)||(l=Object(r.a)(t))||e&&t&&"number"==typeof t.length)return l&&(t=l),s=0,{s:e=function(){},n:function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}},e:function(t){throw t},f:e};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c=a("5530"),m=(a("fb6a"),a("d81d"),a("a9e3"),a("14d9"),a("a434"),a("99af"),a("a4d3"),a("e01a"),a("4ec9"),a("d3b7"),a("3ca3"),a("ddb0"),a("4de4"),a("25f0"),a("159b"),a("b64b"),a("e9c4"),a("13d5"),a("ac1f"),a("5319"),a("0f0e")),d=a("2f62"),u=a("b76a"),p=(u=a.n(u),a("b0e7")),f=a("5334"),h=a("e449"),_=a("6fd3"),v=a("c4ad"),b=(a("00b4"),a("c4c8")),g={name:"taoBao",data:function(){return{soure_link:"",spinShow:!1,grid:{xl:8,lg:8,md:12,sm:24,xs:24},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},copyConfig:{copy_type:2,copy_num:0},artFrom:{type:"taobao",url:""}}},computed:{},created:function(){},mounted:function(){this.getCopyConfig()},methods:{mealPay:function(){this.$router.push({path:this.$routeProStr+"/setting/sms/sms_config/index"})},getCopyConfig:function(){var t=this;Object(b.h)().then((function(e){t.copyConfig.copy_type=e.data.copy_type,t.copyConfig.copy_num=e.data.copy_num}))},add:function(){var t=this;if(this.soure_link){if(!/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(this.soure_link))return this.$message.warning("请输入以http开头的地址！");this.spinShow=!0,this.artFrom.url=this.soure_link,Object(b.i)(this.artFrom).then((function(e){e=e.data.productInfo,t.$emit("on-close",e),t.spinShow=!1})).catch((function(e){t.spinShow=!1,t.$message.error(e.msg)}))}else this.$message.warning("请输入链接地址！")}}},y=(a("49e5"),a("2877")),V=(g=Object(y.a)(g,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.spinShow,expression:"spinShow"}],staticClass:"Box"},[e("div",[e("div",{staticClass:"tips"},[t._v("\n      生成的商品默认是没有上架的，请手动上架商品！\n      "),2==t.copyConfig.copy_type?e("a",{attrs:{href:"https://doc.crmeb.com/single/v5/7785",target:"_blank"}},[t._v("如何配置密钥")]):e("span",[t._v("您当前剩余"+t._s(t.copyConfig.copy_num)+"条采集次数，"),e("span",{staticClass:"add",on:{click:function(e){return t.mealPay()}}},[t._v("增加采集次数")])])]),e("div",[t._v("商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置")])]),e("el-form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{"label-width":"80px","label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"链接地址："}},[e("el-input",{staticClass:"numPut",attrs:{clearable:"",placeholder:"请输入链接地址"},model:{value:t.soure_link,callback:function(e){t.soure_link=e},expression:"soure_link"}}),e("el-button",{staticClass:"ml15",attrs:{type:"primary"},on:{click:t.add}},[t._v("确定")])],1)],1)],1)}),[],!1,null,"e911d200",null).exports,a("a069")),k=a("c24f"),x=a("d708"),w=a("c276"),C=a("0e5c");f={name:"product_productAdd",components:{uploadPictures:p.a,freightTemplate:f.a,addAttr:_.a,couponList:h.a,taoBao:g,draggable:u.a,goodsList:v.default,WangEditor:V.a,userLabel:m.a},data:function(){return{labelShow:!1,dataLabel:[],headTab:[{tit:"基础信息",name:"1"},{tit:"规格库存",name:"2"},{tit:"商品详情",name:"3"},{tit:"物流设置",name:"4"},{tit:"营销设置",name:"5"},{tit:"其他设置",name:"6"}],virtual:[{tit:"普通商品",id:0,tit2:"物流发货"},{tit:"卡密/网盘",id:1,tit2:"自动发货"},{tit:"优惠券",id:2,tit2:"自动发货"},{tit:"虚拟商品",id:3,tit2:"虚拟发货"}],seletVideo:0,customBtn:!1,content:"",contents:"",fileUrl:x.a.apiBaseURL+"/file/upload",fileUrl2:x.a.apiBaseURL+"/file/video_upload",cardUrl:x.a.apiBaseURL+"/file/upload/1",upload_type:"",uploadData:{},header:{},type:0,modals:!1,goods_modals:!1,spinShow:!1,openSubimit:!1,virtualData:"",virtualList:[{key:"",value:""}],grid2:{xl:10,lg:12,md:12,sm:24,xs:24},grid3:{xl:18,lg:18,md:20,sm:24,xs:24},oneFormBatch:[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:"",weight:0,volume:0,virtual_list:[]}],formDynamic:{attrsName:"",attrsVal:""},disk_type:1,tabIndex:0,tabName:"",formDynamicNameData:[],isBtn:!1,columns2:[{title:"图片",slot:"pic",align:"center",minWidth:80},{title:"售价",slot:"price",align:"center",minWidth:95},{title:"成本价",slot:"cost",align:"center",minWidth:95},{title:"原价",slot:"ot_price",align:"center",minWidth:95},{title:"库存",slot:"stock",align:"center",minWidth:95},{title:"商品编号",slot:"bar_code",align:"center",minWidth:120},{title:"重量（KG）",slot:"weight",align:"center",minWidth:95},{title:"体积(m³)",slot:"volume",align:"center",minWidth:95},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:120}],columns3:[{title:"图片",slot:"pic",align:"center",minWidth:80},{title:"售价",slot:"price",align:"center",minWidth:95},{title:"成本价",slot:"cost",align:"center",minWidth:95},{title:"原价",slot:"ot_price",align:"center",minWidth:95},{title:"库存",slot:"stock",align:"center",minWidth:95},{title:"商品编号",slot:"bar_code",align:"center",minWidth:120},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:120}],columns:[],columnsInstall:[],columnsInstal2:[],gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},CustomList:[{value:"text",label:"文本框"},{value:"number",label:"数字"},{value:"email",label:"邮件"},{value:"data",label:"日期"},{value:"time",label:"时间"},{value:"id",label:"身份证"},{value:"phone",label:"手机号"},{value:"img",label:"图片"}],customess:{content:[]},formValidate:{disk_info:"",logistics:["1"],freight:2,postage:0,recommend:[],presale_day:1,presale:!1,is_limit:!1,limit_type:0,limit_num:0,video_open:!1,vip_product:!1,custom_form:[],store_name:"",cate_id:[],label_id:[],keyword:"",unit_name:"",store_info:"",image:"",recommend_image:"",slider_image:[],description:"",ficti:0,give_integral:0,sort:0,is_show:1,is_hidden:0,is_hot:0,is_benefit:0,is_best:0,is_new:0,is_good:0,is_postage:0,is_sub:[],recommend_list:[],virtual_type:0,id:0,spec_type:0,is_virtual:0,video_link:"",temp_id:"",attrs:[],items:[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:""}],activity:["默认","秒杀","砍价","拼团"],couponName:[],header:[],selectRule:"",coupon_ids:[],command_word:"",min_qty:1},ruleList:[],templateList:[],createBnt:!0,showIput:!1,manyFormValidate:[],oneFormValidate:[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:"",weight:0,volume:0,brokerage:0,brokerage_two:0,vip_price:0,virtual_list:[],coupon_id:0}],images:[],imagesTable:"",currentTab:"1",isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},loading:!1,modalPic:!1,addVirtualModel:!1,template:!1,uploadList:[],treeSelect:[],picTit:"",tableIndex:0,ruleValidate:{store_name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cate_id:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],unit_name:[{required:!0,message:"请输入单位",trigger:"blur"}],slider_image:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],spec_type:[{required:!0,message:"请选择商品规格",trigger:"change"}],is_virtual:[{required:!0,message:"请选择商品类型",trigger:"change"}],selectRule:[{required:!0,message:"请选择商品规格属性",trigger:"change"}],temp_id:[{required:!0,message:"请选择运费模板",trigger:"change",type:"number"}],presale_time:[{required:!0,type:"array",message:"请选择活动时间",trigger:"change"}],logistics:[{required:!0,type:"array",min:1,message:"请选择物流方式",trigger:"change"},{type:"array",max:2,message:"请选择物流方式",trigger:"change"}],give_integral:[{type:"integer",message:"请输入整数"}]},manyBrokerage:0,manyBrokerageTwo:0,manyVipPrice:0,upload:{videoIng:!1},videoIng:!1,progress:0,stock:0,disk_info:"",videoLink:"",attrs:[],activity:{"默认":"red","秒杀":"blue","砍价":"green","拼团":"yellow"},couponName:[],updateIds:[],updateName:[],couponIds:"",couponNames:[],rakeBack:[{title:"一级返佣",slot:"brokerage",align:"center",width:95},{title:"二级返佣",slot:"brokerage_two",align:"center",width:95}],member:[{title:"会员价",slot:"vip_price",align:"center",width:95}],columnsInstalM:[],moveIndex:""}},computed:Object(c.a)(Object(c.a)({},Object(d.d)("media",["isMobile"])),{},{labelWidth:function(){return this.isMobile?void 0:"120px"},labelPosition:function(){return this.isMobile?"top":"right"},labelBottom:function(){return this.isMobile?void 0:"15px"}}),beforeRouteUpdate:function(t,e,a){this.bus.$emit("onTagsViewRefreshRouterView",this.$route.path),a()},created:function(){this.columns=this.columns2.slice(0,8),this.getToken()},mounted:function(){var t=this;"0"!==this.$route.params.id&&this.$route.params.id?this.getInfo():"0"===this.$route.params.id&&Object(b.p)().then((function(e){var a,i;e=e.data.info;Array.isArray(e)||(a=e.cate_id.map(Number),e.label_id.map(Number),t.attrs=e.items||[],i=[],e.coupons&&(e.coupons.map((function(t){i.push(t.id)})),t.couponName=e.coupons),t.formValidate=e,t.dataLabel=e.label_id,t.formValidate.coupon_ids=i,t.updateIds=i,t.updateName=e.coupons,t.formValidate.cate_id=a,t.oneFormValidate=e.attrs,t.formValidate.logistics=e.logistics||["1"],t.formValidate.header=[],t.generate(0),t.manyFormValidate=e.attrs,t.spec_type=e.spec_type,t.formValidate.is_virtual=e.is_virtual,t.formValidate.custom_form=e.custom_form||[],0!=t.formValidate.custom_form.length&&(t.customBtn=!0),t.virtualbtn(e.virtual_type,1),0===e.spec_type?t.manyFormValidate=[]:(t.createBnt=!0,t.oneFormValidate=[{pic:e.image,price:0,cost:0,ot_price:0,stock:0,bar_code:"",weight:0,volume:0,brokerage:0,brokerage_two:0,vip_price:0,virtual_list:[],coupon_id:0}]),t.spinShow=!1)})).catch((function(e){t.$message.error(e.msg)})),this.$route.query.type?(this.modals=!0,this.type=this.$route.query.type):this.type=0,this.goodsCategory(),this.productGetRule(),this.productGetTemplate(),this.uploadType()},methods:(p={videoSaveToUrl:function(t){var e=this;return Object(C.a)({file:t,pieceSize:3,success:function(t){e.formValidate.video_link=t.file_path,e.progress=100},error:function(t){e.$message.error(t.msg)},uploading:function(t,a){e.videoIng=!0,t=Math.floor(t/a*100),e.progress=t}}),!1},virtualbtn:function(t,e){var a=this;switch(1!=e&&(this.formValidate.is_sub=[],(e=this.$route.params.id)?Object(b.g)(e).then((function(t){})).catch((function(t){a.formValidate.spec_type=a.spec_type,a.$message.error(t.msg)})):1==this.formValidate.spec_type&&this.generate(1)),t){case 0:this.formValidate.virtual_type=0,this.formValidate.is_virtual=0,this.headTab=[{tit:"基础信息",name:"1"},{tit:"规格库存",name:"2"},{tit:"商品详情",name:"3"},{tit:"物流设置",name:"4"},{tit:"营销设置",name:"5"},{tit:"其他设置",name:"6"}];break;case 1:this.formValidate.virtual_type=1,this.formValidate.postage=0,this.formValidate.is_virtual=1,this.headTab=[{tit:"基础信息",name:"1"},{tit:"规格库存",name:"2"},{tit:"商品详情",name:"3"},{tit:"营销设置",name:"4"},{tit:"其他设置",name:"5"}];break;case 2:this.formValidate.virtual_type=2,this.formValidate.is_virtual=1,this.headTab=[{tit:"基础信息",name:"1"},{tit:"规格库存",name:"2"},{tit:"商品详情",name:"3"},{tit:"营销设置",name:"4"},{tit:"其他设置",name:"5"}];break;case 3:this.formValidate.virtual_type=3,this.formValidate.is_virtual=1,this.headTab=[{tit:"基础信息",name:"1"},{tit:"规格库存",name:"2"},{tit:"商品详情",name:"3"},{tit:"营销设置",name:"4"},{tit:"其他设置",name:"5"}]}},addCate:function(){var t=this;this.$modalForm(Object(b.q)()).then((function(){return t.goodsCategory()}))},logisticsBtn:function(t){this.formValidate.logistics=t},addLabel:function(){var t=this;this.$modalForm(Object(k.O)(0)).then((function(){return t.userLabel()}))},customMessBtn:function(t){t||(this.formValidate.custom_form=[])},addcustom:function(){9<this.formValidate.custom_form.length?this.$message.warning("最多添加10条"):this.formValidate.custom_form.push({title:"",label:"text",value:"",status:!1})},delcustom:function(t){this.formValidate.custom_form.splice(t,1)},onchangeTime:function(t){this.formValidate.presale_time=t},getEditorContent:function(t){this.content=t},cancel:function(){this.modals=!1},getToken:function(){this.header["Authori-zation"]="Bearer "+Object(w.c)("token")},upFile:function(t){var e=this;Object(b.n)({file:t.data.src}).then((function(t){e.virtualList=e.virtualList.concat(t.data)}))},uploadType:function(){var t=this;Object(b.I)().then((function(e){t.upload_type=e.data.upload_type}))},infoData:function(t,e){var a=t.cate_id.map(Number),i=(t.label_id.map(Number),this.attrs=t.items||[],[]);t.coupons.map((function(t){i.push(t.id)})),this.formValidate=t,this.seletVideo=t.seletVideo,this.contents=t.description,this.couponName=t.coupons,this.formValidate.coupon_ids=i,this.updateIds=i,this.dataLabel=t.label_id,this.updateName=t.coupons,this.virtualbtn(t.virtual_type,1),this.formValidate.logistics=t.logistics||["1"],this.formValidate.custom_form=t.custom_form||[],0!=this.formValidate.custom_form.length&&(this.customBtn=!0),this.formValidate.cate_id=a,t.attr&&(this.oneFormValidate=[t.attr]),this.formValidate.header=[],this.generate(0,e,t.attrs),this.spec_type=t.spec_type,this.formValidate.is_virtual=t.is_virtual,0===t.spec_type?this.manyFormValidate=[]:(this.createBnt=!0,this.oneFormValidate=[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:"",weight:0,volume:0,brokerage:0,brokerage_two:0,vip_price:0,virtual_list:[],coupon_id:0}])},checkMove:function(t){this.moveIndex=t.draggedContext.index},end:function(){this.moveIndex="",this.generate(1)},checkAllGroupChange:function(t){this.checkAllGroup(t)},checkAllGroup:function(t){0===this.formValidate.spec_type?(-1<t.indexOf(0)?this.columnsInstall=this.columns2.slice(0,4).concat(this.member):-1<t.indexOf(1)?this.columnsInstall=this.columns2.slice(0,4).concat(this.rakeBack):this.columnsInstall=this.columns2.slice(0,4),2===t.length&&(this.columnsInstall=this.columns2.slice(0,4).concat(this.rakeBack).concat(this.member))):(-1<t.indexOf(0)?this.columnsInstal2=this.columnsInstalM.slice(0,4).concat(this.member):-1<t.indexOf(1)?this.columnsInstal2=this.columnsInstalM.slice(0,4).concat(this.rakeBack):this.columnsInstal2=this.columnsInstalM.slice(0,4),2===t.length&&(this.columnsInstal2=this.columnsInstalM.slice(0,4).concat(this.rakeBack).concat(this.member)))},addCoupon:function(){this.$refs.couponTemplates.isTemplate=!0,this.$refs.couponTemplates.tableList()},see:function(t,e,a){this.tabName=e,this.tabIndex=a,1===this.formValidate.virtual_type?(""!=t.disk_info?(this.disk_type=1,this.disk_info=t.disk_info,this.stock=t.stock):t.virtual_list.length&&(this.disk_type=2,this.virtualList=t.virtual_list),this.addVirtualModel=!0):(this.$refs.goodsCoupon.isTemplate=!0,this.$refs.goodsCoupon.tableList(3))},addGoodsCoupon:function(t,e){this.tabIndex=t,this.tabName=e,this.$refs.goodsCoupon.isTemplate=!0,this.$refs.goodsCoupon.tableList(3)},addVirtual:function(t,e){this.tabIndex=t,this.tabName=e,this.addVirtualModel=!0},upVirtual:function(){if(2==this.disk_type){for(var t=0;t<this.virtualList.length;t++)if(!this.virtualList[t].value)return void this.$message.error("请输入所有卡密");this.$set(this[this.tabName][this.tabIndex],"virtual_list",this.virtualList),this.$set(this[this.tabName][this.tabIndex],"stock",this.virtualList.length),this.virtualList=[{key:"",value:""}],this.$set(this[this.tabName][this.tabIndex],"disk_info","")}else{if(!this.disk_info.length)return this.$message.error("请填写卡密信息");if(!this.stock)return this.$message.error("请填写库存数量");this.$set(this[this.tabName][this.tabIndex],"stock",Number(this.stock)),this.$set(this[this.tabName][this.tabIndex],"stock",Number(this.stock)),this.$set(this[this.tabName][this.tabIndex],"disk_info",this.disk_info),this.$set(this[this.tabName][this.tabIndex],"virtual_list",[])}this.addVirtualModel=!1,this.closeVirtual()},closeVirtual:function(){this.addVirtualModel=!1,this.virtualList=[{key:"",value:""}],this.disk_info="",this.stock=0},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},nameId:function(t,e){this.formValidate.coupon_ids=t,this.couponName=this.unique(e)},goodsCouponId:function(t){this.$set(this[this.tabName][this.tabIndex],"coupon_id",t.id),this.$set(this[this.tabName][this.tabIndex],"coupon_name",t.title),this.$refs.goodsCoupon.isTemplate=!1},handleClose:function(t){t=this.couponName.indexOf(t);var e=(this.couponName.splice(t,1),this.formValidate.coupon_ids);e.splice(t,1),this.updateIds=e,this.updateName=this.couponName},getList:function(){this.productGetTemplate()},addTemp:function(){this.$refs.templates.isTemplate=!0},delVideo:function(){var t=this;t.$set(t.formValidate,"video_link",""),t.$set(t,"progress",0),t.videoIng=!1,t.upload.videoIng=!1},zh_uploadFile:function(){1==this.seletVideo?this.formValidate.video_link=this.videoLink:this.$refs.refid.click()},zh_uploadFile_change:function(t){var e=this;if(-1===t.target.files[0].name.substr(t.target.files[0].name.indexOf(".")).indexOf(".mp4"))return e.$message.error("只能上传MP4文件");var a={key:t.target.files[0].name,contentType:t.target.files[0].type};Object(b.t)(a).then((function(a){e.$videoCloud.videoUpload({type:a.data.type,evfile:t,res:a,uploading:function(t,i){e.upload.videoIng=t,200==a.status&&(e.progress=100)}}).then((function(t){e.formValidate.video_link=t.url,e.$message.success("视频上传成功"),e.upload.videoIng=!1})).catch((function(t){e.$message.error(t)}))})).catch((function(t){e.$message.error(t.msg)}))},upTab:function(){this.currentTab=(Number(this.currentTab)-1).toString()},downTab:function(){this.currentTab=(Number(this.currentTab)+1).toString()},userSearchs:function(){this.productGetRule()},addRule:function(){this.$refs.addattr.modal=!0},brokerageSetUp:function(){var t=this;if(-1<t.formValidate.is_sub.indexOf(1)){if(t.manyBrokerage<=0||t.manyBrokerageTwo<=0)return t.$message.error("请填写返佣金额后进行批量添加")}else if(-1<t.formValidate.is_sub.indexOf(0)&&t.manyVipPrice<=0)return t.$message.error("请填写会员价后进行批量添加");if(2===this.formValidate.is_sub.length&&(t.manyBrokerage<=0||t.manyBrokerageTwo<=0||t.manyVipPrice<=0))return t.$message.error("请填写完金额后进行批量添加");var e,a=n(t.manyFormValidate);try{for(a.s();!(e=a.n()).done;){var i=e.value;this.$set(i,"brokerage",t.manyBrokerage),this.$set(i,"brokerage_two",t.manyBrokerageTwo),this.$set(i,"vip_price",t.manyVipPrice)}}catch(t){a.e(t)}finally{a.f()}},vipPriceSetUp:function(){if(this.manyVipPrice<=0)return this.$message.error("请填写会员价在进行批量添加");var t,e=n(this.manyFormValidate);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.$set(a,"vip_price",this.manyVipPrice)}}catch(t){e.e(t)}finally{e.f()}},handleAdd:function(){this.virtualList.push({key:"",value:""})},initVirtualData:function(t){this.virtualList=[{key:"",value:""}]},removeVirtual:function(t){this.virtualList.splice(t,1)},batchDel:function(){this.oneFormBatch=[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:"",weight:0,volume:0,virtual_list:[]}]},confirm:function(){var t=this;if(t.createBnt=!0,t.formValidate.selectRule.trim().length<=0)return t.$message.error("请选择属性");t.ruleList.forEach((function(e,a){e.rule_name===t.formValidate.selectRule&&(t.attrs=e.rule_value)}))},productGetRule:function(){var t=this;Object(b.s)().then((function(e){t.ruleList=e.data}))},productGetTemplate:function(){var t=this;Object(b.u)().then((function(e){t.templateList=e.data}))},delAttrTable:function(t){var e=this,a=this.$route.params.id;a?Object(b.g)(a).then((function(a){e.manyFormValidate.splice(t,1),e.$message.success(a.msg)})).catch((function(t){e.$message.error(t.msg)})):this.manyFormValidate.splice(t,1)},batchAdd:function(){var t,e=n(this.manyFormValidate);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.oneFormBatch[0].pic&&this.$set(a,"pic",this.oneFormBatch[0].pic),0<this.oneFormBatch[0].price&&this.$set(a,"price",this.oneFormBatch[0].price),0<this.oneFormBatch[0].cost&&this.$set(a,"cost",this.oneFormBatch[0].cost),0<this.oneFormBatch[0].ot_price&&this.$set(a,"ot_price",this.oneFormBatch[0].ot_price),0<this.oneFormBatch[0].stock&&this.$set(a,"stock",this.oneFormBatch[0].stock),""!==this.oneFormBatch[0].bar_code&&this.$set(a,"bar_code",this.oneFormBatch[0].bar_code),0<this.oneFormBatch[0].weight&&this.$set(a,"weight",this.oneFormBatch[0].weight),0<this.oneFormBatch[0].volume&&this.$set(a,"volume",this.oneFormBatch[0].volume)}}catch(t){e.e(t)}finally{e.f()}},addBtn:function(){this.clearAttr(),this.createBnt=!1,this.showIput=!0},generate:function(t,e,a){var i=this;Object(b.k)({attrs:this.attrs,is_virtual:[1,2].includes(this.formValidate.virtual_type)?1:0,virtual_type:this.formValidate.virtual_type},this.formValidate.id,t).then((function(o){o=o.data.info;var s=JSON.parse(JSON.stringify(o.header)),l=("0"===i.$route.params.id||-1==i.$route.query.type&&!t||e||(i.manyFormValidate=o.value),e&&(i.manyFormValidate=a),o.header);[1,2].includes(i.formValidate.virtual_type)?(i.columnsInstalM=l,i.formValidate.header=l):(i.formValidate.header=s,i.columnsInstalM=o.header),i.checkAllGroup(i.formValidate.is_sub),i.$route.params.id||1!==i.formValidate.spec_type||e?i.$route.params.id&&(i.manyFormValidate.map((function(t){t.pic||(t.pic=i.formValidate.image)})),i.oneFormBatch[0].pic=i.formValidate.image):(i.manyFormValidate.map((function(t){t.pic=i.formValidate.image})),i.oneFormBatch[0].pic=i.formValidate.image)})).catch((function(t){i.$message.error(t.msg)}))},offAttrName:function(){this.showIput=!1,this.createBnt=!0},clearAttr:function(){this.formDynamic.attrsName="",this.formDynamic.attrsVal=""},handleRemoveRole:function(t){this.attrs.splice(t,1),this.manyFormValidate.splice(t,1)},handleRemove2:function(t,e){t.splice(e,1)},createAttrName:function(){var t,e;this.formDynamic.attrsName&&this.formDynamic.attrsVal?(t={value:this.formDynamic.attrsName,detail:[this.formDynamic.attrsVal]},this.attrs.push(t),e={},this.attrs=this.attrs.reduce((function(t,a){return e[a.value]||(e[a.value]=t.push(a)),t}),[]),this.clearAttr(),this.showIput=!1,this.createBnt=!0):this.$message.warning("请添加完整的规格！")},createAttr:function(t,e){var a;t?(this.attrs[e].detail.push(t),a={},this.attrs[e].detail=this.attrs[e].detail.reduce((function(t,e){return a[e]||(a[e]=t.push(e)),t}),[])):this.$message.warning("请添加属性")},goodsCategory:function(){var t=this;Object(b.e)(1).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$message.error(e.msg)}))},changeVideo:function(t){this.formValidate.video_link="",this.videoLink=""},changeSpec:function(){var t=this,e=(this.formValidate.is_sub=[],this.$route.params.id);e&&Object(b.g)(e).then((function(t){})).catch((function(e){t.formValidate.spec_type=t.spec_type,t.$message.error(e.msg)}))},getInfo:function(){var t=this;this.spinShow=!0,Object(b.v)(this.$route.params.id).then(function(){var e=Object(l.a)(Object(s.a)().mark((function e(a){var i;return Object(s.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=a.data.productInfo,t.infoData(i),t.spinShow=!1;case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.spinShow=!1,t.$message.error(e.msg)}))},handleRemove:function(t){this.images.splice(t,1),this.formValidate.slider_image.splice(t,1),this.oneFormValidate[0].pic=this.formValidate.slider_image[0]},changeCancel:function(t){this.modalPic=!1},modalPicTap:function(t,e,a){this.modalPic=!0,this.isChoice="dan"===t?"单选":"多选",this.picTit=e,this.tableIndex=a},getPic:function(t){switch(this.picTit){case"danFrom":this.formValidate.image=t.att_dir,this.$route.params.id||(0===this.formValidate.spec_type?this.oneFormValidate[0].pic=t.att_dir:(this.manyFormValidate.map((function(e){e.pic=t.att_dir})),this.oneFormBatch[0].pic=t.att_dir));break;case"danTable":this.oneFormValidate[this.tableIndex].pic=t.att_dir;break;case"duopi":this.oneFormBatch[this.tableIndex].pic=t.att_dir;break;case"recommend_image":this.formValidate.recommend_image=t.att_dir;break;default:this.manyFormValidate[this.tableIndex].pic=t.att_dir}this.modalPic=!1},getPicD:function(t){var e=this;this.images=t,this.images.map((function(t){e.formValidate.slider_image.push(t.att_dir),e.formValidate.slider_image=e.formValidate.slider_image.splice(0,10)})),this.oneFormValidate[0].pic=this.formValidate.slider_image[0],this.modalPic=!1},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return e.formValidate.store_name?e.formValidate.cate_id.length?e.formValidate.unit_name?e.formValidate.slider_image.length?e.formValidate.logistics.length||e.formValidate.virtual_type?e.formValidate.temp_id||3!=e.formValidate.freight?void 0:e.$message.warning("商品信息-运费模板不能为空"):e.$message.warning("物流设置-至少选择一种物流方式"):e.$message.warning("商品信息-商品轮播图不能为空"):e.$message.warning("商品信息-商品单位不能为空"):e.$message.warning("商品信息-商品分类不能为空"):e.$message.warning("商品信息-商品名称不能为空");if(e.formValidate.type=e.type,0===e.formValidate.spec_type?(e.formValidate.attrs=e.oneFormValidate,e.formValidate.header=[],e.formValidate.items=[],e.formValidate.is_copy=0):(e.formValidate.items=e.attrs,e.formValidate.attrs=e.manyFormValidate,e.formValidate.is_copy=1),1===e.formValidate.spec_type&&0===e.manyFormValidate.length)return e.$message.warning("商品信息-请点击生成多规格");for(var a=e.formValidate.attrs,i=0;i<a.length;i++)if(1e6<a[i].stock)return e.$message.error("规格库存-库存超出系统范围(1000000)");if(1===e.formValidate.is_sub[0]){for(var o=0;o<a.length;o++)if(null===a[o].brokerage||null===a[o].brokerage_two)return e.$message.error("营销设置- 一二级返佣不能为空")}else for(var r=0;r<a.length;r++)if(null===a[r].vip_price)return e.$message.error("营销设置-会员价不能为空");if(2===e.formValidate.is_sub.length)for(var n=0;n<a.length;n++)if(null===a[n].brokerage||null===a[n].brokerage_two||null===a[n].vip_price)return e.$message.error("营销设置- 一二级返佣和会员价不能为空");if(3==e.formValidate.freight&&!e.formValidate.temp_id)return e.$message.warning("商品信息-运费模板不能为空");var c=[];e.dataLabel.forEach((function(t){c.push(t.id)})),e.formValidate.label_id=c,e.openSubimit||(e.openSubimit=!0,e.formValidate.description=e.formatRichText(e.content),Object(b.o)(e.formValidate).then(function(){var t=Object(l.a)(Object(s.a)().mark((function t(a){return Object(s.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.openSubimit=!1,e.$message.success(a.msg),"0"===e.$route.params.id&&Object(b.d)().catch((function(t){e.$message.error(t.msg)})),setTimeout((function(){e.openSubimit=!1,e.$router.push({path:e.$routeProStr+"/product/product_list"})}),500);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){setTimeout((function(t){e.openSubimit=!1}),1e3),e.$message.error(t.msg)})))}))},changeTemplate:function(t){this.template=t},validate:function(t,e,a){!1===e&&this.$message.warning(a)},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){var a;t.dataTransfer.effectAllowed="move",e!==this.dragging&&(a=(t=Object(o.a)(this.formValidate.slider_image)).indexOf(this.dragging),e=t.indexOf(e),t.splice.apply(t,[e,0].concat(Object(o.a)(t.splice(a,1)))),this.formValidate.slider_image=t)},formatRichText:function(t){return t=t.replace(/<img[^>]*>/gi,(function(t,e){return(t=(t=t.replace(/style="[^"]+"/gi,"").replace(/style='[^']+'/gi,"")).replace(/width="[^"]+"/gi,"").replace(/width='[^']+'/gi,"")).replace(/height="[^"]+"/gi,"").replace(/height='[^']+'/gi,"")})),(t=(t=t.replace(/style="[^"]+"/gi,(function(t,e){return t.replace(/width:[^;]+;/gi,"max-width:100%;").replace(/width:[^;]+;/gi,"max-width:100%;")}))).replace(/<br[^>]*\/>/gi,"")).replace(/\<img/gi,'<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"')}},Object(i.a)(p,"unique",(function(t){var e=new Map;return t.filter((function(t){return!e.has(t.product_id)&&e.set(t.product_id,1)}))})),Object(i.a)(p,"getProductId",(function(t){this.goods_modals=!1,this.formValidate.recommend_list=this.unique(this.formValidate.recommend_list.concat(t))})),Object(i.a)(p,"changeGoods",(function(){this.goods_modals=!0,this.$refs.goodslist.getList(),this.$refs.goodslist.goodsCategory()})),Object(i.a)(p,"activeData",(function(t){this.labelShow=!1,this.dataLabel=t})),Object(i.a)(p,"labelClose",(function(){this.labelShow=!1})),Object(i.a)(p,"closeLabel",(function(t){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id==t.id}))[0]);this.dataLabel.splice(e,1)})),Object(i.a)(p,"openLabel",(function(t){this.labelShow=!0,this.$nextTick((function(t){}))})),Object(i.a)(p,"uniques",(function(t){for(var e,a={},i=[],o=0;o<t.length;o++)a[t[o].product_id]=t[o];for(e in a)i.push(a[e]);return i})),Object(i.a)(p,"handleRemoveRecommend",(function(t){this.formValidate.recommend_list.splice(t,1)})),p)},a("5068"),a("3c62"),_=Object(y.a)(f,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.spinShow,expression:"spinShow"}],attrs:{id:"shopp-manager"}},[e("pages-header",{ref:"pageHeader",attrs:{title:t.$route.params.id?"编辑商品":"添加商品",backUrl:t.$routeProStr+"/product/product_list"}}),e("el-card",{staticClass:"mt16",attrs:{bordered:!1,shadow:"never","body-style":{padding:"0 20px 20px"}}},[e("el-tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.headTab,(function(t,a){return e("el-tab-pane",{key:a,attrs:{label:t.tit,name:t.name}})})),1),e("el-form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab,expression:"currentTab === '1'"}],attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品类型：",props:"is_virtual"}},t._l(t.virtual,(function(a,i){return e("div",{key:i,staticClass:"virtual",class:t.formValidate.virtual_type==a.id?"virtual_boder":"virtual_boder2",on:{click:function(e){return t.virtualbtn(a.id,2)}}},[e("div",{staticClass:"virtual_top"},[t._v(t._s(a.tit))]),e("div",{staticClass:"virtual_bottom"},[t._v("("+t._s(a.tit2)+")")]),t.formValidate.virtual_type==a.id?e("div",{staticClass:"virtual_san"}):t._e(),t.formValidate.virtual_type==a.id?e("div",{staticClass:"virtual_dui"},[t._v("✓")]):t._e()])})),0)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品分类：",prop:"cate_id"}},[e("el-cascader",{staticClass:"content_width",attrs:{size:"small",options:t.treeSelect,props:{multiple:!0,checkStrictly:!0,emitPath:!1},clearable:""},model:{value:t.formValidate.cate_id,callback:function(e){t.$set(t.formValidate,"cate_id",e)},expression:"formValidate.cate_id"}}),e("span",{staticClass:"addfont",on:{click:t.addCate}},[t._v("新增分类")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品名称：",prop:"store_name"}},[e("el-input",{staticClass:"content_width",attrs:{placeholder:"请输入商品名称"},model:{value:t.formValidate.store_name,callback:function(e){t.$set(t.formValidate,"store_name","string"==typeof e?e.trim():e)},expression:"formValidate.store_name"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"单位：",prop:"unit_name"}},[e("el-input",{staticClass:"content_width",attrs:{placeholder:"请输入单位"},model:{value:t.formValidate.unit_name,callback:function(e){t.$set(t.formValidate,"unit_name",e)},expression:"formValidate.unit_name"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品轮播图：",prop:"slider_image"}},[e("div",{staticClass:"acea-row"},[t._l(t.formValidate.slider_image,(function(a,i){return e("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(e){return t.handleDragStart(e,a)},dragover:function(e){return e.preventDefault(),t.handleDragOver(e,a)},dragenter:function(e){return t.handleDragEnter(e,a)},dragend:function(e){return t.handleDragEnd(e,a)}}},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a,expression:"item"}]}),e("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i)}}})])})),t.formValidate.slider_image.length<10?e("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap("duo")}}},[e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})]):t._e(),e("el-input",{staticStyle:{display:"none"},model:{value:t.formValidate.slider_image[0],callback:function(e){t.$set(t.formValidate.slider_image,0,e)},expression:"formValidate.slider_image[0]"}})],2),e("div",{staticClass:"titTip"},[t._v("建议尺寸：800*800，可拖拽改变图片顺序，默认首张图为主图，最多上传10张")])])],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"添加视频："}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"large"},model:{value:t.formValidate.video_open,callback:function(e){t.$set(t.formValidate,"video_open",e)},expression:"formValidate.video_open"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1),t.formValidate.video_open?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"视频类型："}},[e("el-radio-group",{on:{input:t.changeVideo},model:{value:t.seletVideo,callback:function(e){t.seletVideo=e},expression:"seletVideo"}},[e("el-radio",{staticClass:"radio",attrs:{label:0}},[t._v("本地视频")]),e("el-radio",{attrs:{label:1}},[t._v("视频链接")])],1)],1)],1):t._e(),t.formValidate.video_open?e("el-col",{attrs:{span:24,id:"selectvideo"}},[e("el-form-item",{attrs:{label:"",prop:"video_link"}},[1!=t.seletVideo||t.formValidate.video_link?t._e():e("el-input",{staticClass:"content_width",attrs:{placeholder:"请输入视频链接"},model:{value:t.videoLink,callback:function(e){t.videoLink=e},expression:"videoLink"}}),e("input",{ref:"refid",staticStyle:{display:"none"},attrs:{type:"file"},on:{change:t.zh_uploadFile_change}}),0!=t.seletVideo||"1"===t.upload_type&&!t.videoLink||t.formValidate.video_link?t._e():e("div",{staticClass:"ml10 videbox",on:{click:t.zh_uploadFile}},[t._v("\n                +\n              ")]),1!=t.seletVideo||"1"===t.upload_type&&!t.videoLink||t.formValidate.video_link?t._e():e("el-button",{staticClass:"ml10",attrs:{type:"primary"},on:{click:t.zh_uploadFile}},[t._v("确认添加")]),"1"!==t.upload_type||t.videoLink?t._e():e("el-upload",{staticStyle:{display:"inline-block"},attrs:{"show-file-list":!1,action:t.fileUrl2,"before-upload":t.videoSaveToUrl,data:t.uploadData,headers:t.header,multiple:!1}},[0!==t.seletVideo||t.formValidate.video_link?t._e():e("div",{staticClass:"videbox"},[t._v("+")])]),t.formValidate.video_link?e("div",{staticClass:"box-video-style"},[e("video",{staticStyle:{width:"100%",height:"100% !important","border-radius":"10px"},attrs:{src:t.formValidate.video_link,controls:"controls"}},[t._v("\n                  您的浏览器不支持 video 标签。\n                ")]),e("div",{staticClass:"mark"}),e("i",{staticClass:"el-icon-delete iconv",on:{click:t.delVideo}})]):t._e(),t.upload.videoIng||t.videoIng?e("Progress",{staticClass:"progress",attrs:{percent:t.progress,"stroke-width":5}}):t._e(),e("div",{staticClass:"titTip"},[t._v("建议时长：9～30秒，视频宽高比16:9")])],1)],1):t._e(),e("el-col",t._b({},"el-col",t.grid,!1),[e("el-form-item",{attrs:{label:"商品状态："}},[e("el-radio-group",{model:{value:t.formValidate.is_show,callback:function(e){t.$set(t.formValidate,"is_show",e)},expression:"formValidate.is_show"}},[e("el-radio",{staticClass:"radio",attrs:{label:1}},[t._v("上架")]),e("el-radio",{attrs:{label:0}},[t._v("下架")])],1)],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"是否隐藏:"}},[e("el-radio-group",{model:{value:t.formValidate.is_hidden,callback:function(e){t.$set(t.formValidate,"is_hidden",e)},expression:"formValidate.is_hidden"}},[e("el-radio",{staticClass:"radio",attrs:{label:0}},[t._v("显示")]),e("el-radio",{attrs:{label:1}},[t._v("隐藏")])],1)],1)],1)],1),e("el-row",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}],attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品规格：",props:"spec_type"}},[e("el-radio-group",{on:{input:t.changeSpec},model:{value:t.formValidate.spec_type,callback:function(e){t.$set(t.formValidate,"spec_type",e)},expression:"formValidate.spec_type"}},[e("el-radio",{staticClass:"radio",attrs:{label:0}},[t._v("单规格")]),e("el-radio",{attrs:{label:1}},[t._v("多规格")])],1)],1)],1),1===t.formValidate.spec_type?e("el-col",{staticClass:"noForm",attrs:{span:24}},[e("el-form-item",{attrs:{label:"选择规格：",prop:""}},[e("div",{staticClass:"acea-row row-middle"},[e("el-select",{staticClass:"content_width mr14",model:{value:t.formValidate.selectRule,callback:function(e){t.$set(t.formValidate,"selectRule",e)},expression:"formValidate.selectRule"}},t._l(t.ruleList,(function(t,a){return e("el-option",{key:a,attrs:{value:t.rule_name,label:t.rule_name}})})),1),e("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确认")]),e("el-button",{on:{click:t.addRule}},[t._v("添加规格模板")])],1)]),0!==t.attrs.length?e("el-form-item",[e("draggable",{staticClass:"dragArea list-group",attrs:{list:t.attrs,group:"peoples",handle:".move-icon",move:t.checkMove},on:{end:t.end}},t._l(t.attrs,(function(a,i){return e("div",{key:i,staticClass:"acea-row row-middle mb10"},[e("div",{staticClass:"move-icon"},[e("span",{staticClass:"iconfont icondrag2"})]),e("div",{class:t.moveIndex===i?"borderStyle":"",staticStyle:{width:"90%"}},[e("div",{staticClass:"acea-row row-middle"},[e("span",{staticClass:"mr5"},[t._v(t._s(a.value))]),e("i",{staticClass:"curs el-icon-error",attrs:{size:"14"},on:{click:function(e){return t.handleRemoveRole(i)}}})]),e("div",{staticClass:"rulesBox"},[e("draggable",{staticClass:"item",attrs:{list:a.detail,handle:".drag"}},t._l(a.detail,(function(i,o){return e("el-tag",{key:o,staticClass:"mr20 drag",attrs:{closable:"",color:"primary"},on:{close:function(e){return t.handleRemove2(a.detail,o)}}},[t._v(t._s(i))])})),1),e("el-input",{staticStyle:{width:"190px"},attrs:{placeholder:"请输入属性名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.createAttr(a.detail.attrsVal,i)}},model:{value:a.detail.attrsVal,callback:function(e){t.$set(a.detail,"attrsVal",e)},expression:"item.detail.attrsVal"}},[e("template",{slot:"append"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createAttr(a.detail.attrsVal,i)}}},[t._v("添加")])],1)],2)],1)])])})),0)],1):t._e(),t.createBnt?e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.addBtn}},[t._v("添加新规格")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.generate(1)}}},[t._v("立即生成")])],1):t._e(),t.showIput?e("el-col",{attrs:{span:24}},[e("el-col",{attrs:{xl:6,lg:9,md:10,sm:24,xs:24}},[e("el-form-item",{attrs:{label:"规格："}},[e("el-input",{attrs:{placeholder:"请输入规格"},model:{value:t.formDynamic.attrsName,callback:function(e){t.$set(t.formDynamic,"attrsName",e)},expression:"formDynamic.attrsName"}})],1)],1),e("el-col",{attrs:{xl:6,lg:9,md:10,sm:24,xs:24}},[e("el-form-item",{attrs:{label:"规格值："}},[e("el-input",{attrs:{placeholder:"请输入规格值"},model:{value:t.formDynamic.attrsVal,callback:function(e){t.$set(t.formDynamic,"attrsVal",e)},expression:"formDynamic.attrsVal"}})],1)],1),e("el-col",{attrs:{xl:6,lg:5,md:10,sm:24,xs:24}},[e("div",{staticClass:"df-n-warp"},[e("el-button",{attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),e("el-button",{on:{click:t.offAttrName}},[t._v("取消")])],1)])],1):t._e(),t.manyFormValidate.length&&0!==t.formValidate.header.length&&0!==t.attrs.length?e("el-col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[[0,3].includes(t.formValidate.virtual_type)?e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"labeltop",attrs:{label:"批量设置："}},[e("el-table",{attrs:{data:t.oneFormBatch,border:""}},t._l(t.formValidate.is_virtual?t.columns3:t.columns2,(function(a,i){return e("el-table-column",{key:i,attrs:{label:a.title,"min-width":a.minWidth},scopedSlots:t._u([{key:"default",fn:function(i){return[a.key?[e("div",[e("span",[t._v(t._s(i.row[a.key]))])])]:"pic"===a.slot?[e("div",{staticClass:"acea-row row-middle row-center-wrapper",on:{click:function(e){return t.modalPicTap("dan","duopi",i.$index)}}},[t.oneFormBatch[0].pic?e("div",{staticClass:"pictrue pictrueTab"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.oneFormBatch[0].pic,expression:"oneFormBatch[0].pic"}]})]):e("div",{staticClass:"upLoad pictrueTab acea-row row-center-wrapper"},[e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})])])]:"price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormBatch[0].price,callback:function(e){t.$set(t.oneFormBatch[0],"price",e)},expression:"oneFormBatch[0].price"}})]:"cost"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormBatch[0].cost,callback:function(e){t.$set(t.oneFormBatch[0],"cost",e)},expression:"oneFormBatch[0].cost"}})]:"ot_price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0},model:{value:t.oneFormBatch[0].ot_price,callback:function(e){t.$set(t.oneFormBatch[0],"ot_price",e)},expression:"oneFormBatch[0].ot_price"}})]:"stock"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,disabled:1==t.formValidate.virtual_type,min:0,max:9999999999},model:{value:t.oneFormBatch[0].stock,callback:function(e){t.$set(t.oneFormBatch[0],"stock",e)},expression:"oneFormBatch[0].stock"}})]:"fictitious"===a.slot?[i.row.coupon_id||2!=t.formValidate.virtual_type?2==t.formValidate.virtual_type&&i.row.coupon_id?e("span",{staticClass:"see",on:{click:function(e){return t.see(i.row,"manyFormValidate",i.$index)}}},[t._v(t._s(i.row.coupon_name))]):i.row.virtual_list.length||1!=t.formValidate.virtual_type?i.row.virtual_list.length&&1==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(i.row,"oneFormBatch",i.$index)}}},[t._v("已设置")]):t._e():e("el-button",{on:{click:function(e){return t.addVirtual(i.$index,"oneFormBatch")}}},[t._v("添加卡密")]):e("el-button",{on:{click:function(e){return t.addGoodsCoupon(i.$index,"oneFormBatch")}}},[t._v("添加优惠券")])]:"bar_code"===a.slot?[e("el-input",{model:{value:t.oneFormBatch[0].bar_code,callback:function(e){t.$set(t.oneFormBatch[0],"bar_code",e)},expression:"oneFormBatch[0].bar_code"}})]:"weight"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,step:.1,min:0,max:9999999999},model:{value:t.oneFormBatch[0].weight,callback:function(e){t.$set(t.oneFormBatch[0],"weight",e)},expression:"oneFormBatch[0].weight"}})]:"volume"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,step:.1,min:0,max:9999999999},model:{value:t.oneFormBatch[0].volume,callback:function(e){t.$set(t.oneFormBatch[0],"volume",e)},expression:"oneFormBatch[0].volume"}})]:"action"===a.slot?[e("a",{on:{click:t.batchAdd}},[t._v("批量添加")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:t.batchDel}},[t._v("清空")])]:t._e()]}}],null,!0)})})),1)],1)],1):t._e(),e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性："}},[e("el-table",{attrs:{data:t.manyFormValidate,border:""}},t._l(t.formValidate.header,(function(a,i){return e("el-table-column",{key:i,attrs:{label:a.title,"min-width":a.minWidth},scopedSlots:t._u([{key:"default",fn:function(i){return[a.key?[e("div",[e("span",[t._v(t._s(i.row[a.key]))])])]:"pic"===a.slot?[e("div",{staticClass:"acea-row row-middle row-center-wrapper",on:{click:function(e){return t.modalPicTap("dan","duoTable",i.$index)}}},[t.manyFormValidate[i.$index].pic?e("div",{staticClass:"pictrue pictrueTab"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.manyFormValidate[i.$index].pic,expression:"manyFormValidate[scope.$index].pic"}]})]):e("div",{staticClass:"upLoad pictrueTab acea-row row-center-wrapper"},[e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})])])]:"price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].price,callback:function(e){t.$set(t.manyFormValidate[i.$index],"price",e)},expression:"manyFormValidate[scope.$index].price"}})]:"cost"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].cost,callback:function(e){t.$set(t.manyFormValidate[i.$index],"cost",e)},expression:"manyFormValidate[scope.$index].cost"}})]:"ot_price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].ot_price,callback:function(e){t.$set(t.manyFormValidate[i.$index],"ot_price",e)},expression:"manyFormValidate[scope.$index].ot_price"}})]:"stock"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,disabled:1==t.formValidate.virtual_type,min:0,max:9999999999,precision:0},model:{value:t.manyFormValidate[i.$index].stock,callback:function(e){t.$set(t.manyFormValidate[i.$index],"stock",e)},expression:"manyFormValidate[scope.$index].stock"}})]:"bar_code"===a.slot?[e("el-input",{model:{value:t.manyFormValidate[i.$index].bar_code,callback:function(e){t.$set(t.manyFormValidate[i.$index],"bar_code",e)},expression:"manyFormValidate[scope.$index].bar_code"}})]:"weight"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].weight,callback:function(e){t.$set(t.manyFormValidate[i.$index],"weight",e)},expression:"manyFormValidate[scope.$index].weight"}})]:"volume"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].volume,callback:function(e){t.$set(t.manyFormValidate[i.$index],"volume",e)},expression:"manyFormValidate[scope.$index].volume"}})]:"fictitious"===a.slot?[i.row.coupon_id||2!=t.formValidate.virtual_type?i.row.coupon_id&&2==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(i.row,"manyFormValidate",i.$index)}}},[t._v(t._s(i.row.coupon_name))]):i.row.virtual_list||i.row.stock||1!=t.formValidate.virtual_type?(i.row.virtual_list.length||i.row.stock)&&1==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(i.row,"manyFormValidate",i.$index)}}},[t._v("已设置")]):t._e():e("el-button",{on:{click:function(e){return t.addVirtual(i.$index,"manyFormValidate")}}},[t._v("添加卡密")]):e("el-button",{on:{click:function(e){return t.addGoodsCoupon(i.$index,"manyFormValidate")}}},[t._v("添加优惠券")])]:"action"===a.slot?[e("a",{on:{click:function(e){return t.delAttrTable(i.$index)}}},[t._v("删除")])]:t._e()]}}],null,!0)})})),1)],1)],1)],1):t._e()],1):t._e(),0===t.formValidate.spec_type?e("div",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"图片："}},[e("div",{staticClass:"pictrueBox",on:{click:function(e){return t.modalPicTap("dan","danTable",0)}}},[t.oneFormValidate[0].pic?e("div",{staticClass:"pictrue"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.oneFormValidate[0].pic,expression:"oneFormValidate[0].pic"}]}),e("el-input",{staticStyle:{display:"none"},model:{value:t.oneFormValidate[0].pic,callback:function(e){t.$set(t.oneFormValidate[0],"pic",e)},expression:"oneFormValidate[0].pic"}})],1):e("div",{staticClass:"upLoad acea-row row-center-wrapper"},[e("el-input",{staticStyle:{display:"none"},model:{value:t.oneFormValidate[0].pic,callback:function(e){t.$set(t.oneFormValidate[0],"pic",e)},expression:"oneFormValidate[0].pic"}}),e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})],1)])])],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"售价："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,precision:2,max:9999999999,"active-change":!1},model:{value:t.oneFormValidate[0].price,callback:function(e){t.$set(t.oneFormValidate[0],"price",e)},expression:"oneFormValidate[0].price"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"成本价："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,precision:2,"active-change":!1},model:{value:t.oneFormValidate[0].cost,callback:function(e){t.$set(t.oneFormValidate[0],"cost",e)},expression:"oneFormValidate[0].cost"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"原价："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,precision:2,"active-change":!1},model:{value:t.oneFormValidate[0].ot_price,callback:function(e){t.$set(t.oneFormValidate[0],"ot_price",e)},expression:"oneFormValidate[0].ot_price"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"库存："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,disabled:1==t.formValidate.virtual_type,precision:0},model:{value:t.oneFormValidate[0].stock,callback:function(e){t.$set(t.oneFormValidate[0],"stock",e)},expression:"oneFormValidate[0].stock"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品编号："}},[e("el-input",{staticClass:"content_width",model:{value:t.oneFormValidate[0].bar_code,callback:function(e){t.$set(t.oneFormValidate[0],"bar_code","string"==typeof e?e.trim():e)},expression:"oneFormValidate[0].bar_code"}})],1)],1),0==t.formValidate.virtual_type?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"重量(KG)："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormValidate[0].weight,callback:function(e){t.$set(t.oneFormValidate[0],"weight",e)},expression:"oneFormValidate[0].weight"}})],1)],1):t._e(),e("el-col",{attrs:{span:24}},[0==t.formValidate.virtual_type?e("el-form-item",{attrs:{label:"体积(m³)："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormValidate[0].volume,callback:function(e){t.$set(t.oneFormValidate[0],"volume",e)},expression:"oneFormValidate[0].volume"}})],1):t._e()],1),e("el-col",{attrs:{span:24}},[1==t.formValidate.virtual_type||2==t.formValidate.virtual_type?e("el-form-item",{attrs:{label:"虚拟商品："}},[t.oneFormValidate[0].coupon_id||2!=t.formValidate.virtual_type?t.oneFormValidate[0].coupon_id&&2==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.oneFormValidate[0],"oneFormValidate",0)}}},[t._v(t._s(t.oneFormValidate[0].coupon_name))]):t._e():e("el-button",{on:{click:function(e){return t.addGoodsCoupon(0,"oneFormValidate")}}},[t._v("添加优惠券")]),t.oneFormValidate[0].virtual_list.length||t.oneFormValidate[0].stock||1!=t.formValidate.virtual_type?(t.oneFormValidate[0].virtual_list.length||0<t.oneFormValidate[0].stock)&&1==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.oneFormValidate[0],"oneFormValidate",0)}}},[t._v("已设置")]):t._e():e("el-button",{on:{click:function(e){return t.addVirtual(0,"oneFormValidate")}}},[t._v("添加卡密")])],1):t._e()],1)],1):t._e()],1),e("el-row",{directives:[{name:"show",rawName:"v-show",value:"3"===t.currentTab,expression:"currentTab === '3'"}]},[e("el-col",{attrs:{span:16}},[e("el-form-item",{attrs:{label:"商品详情："}},[e("WangEditor",{staticStyle:{width:"100%"},attrs:{content:t.contents},on:{editorContent:t.getEditorContent}})],1)],1),e("el-col",{staticStyle:{width:"33%"},attrs:{span:6}},[e("div",{staticClass:"ifam"},[e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.content)}})])])],1),e("el-row",{directives:[{name:"show",rawName:"v-show",value:6===t.headTab.length&&"4"===t.currentTab,expression:"headTab.length === 6 ? currentTab === '4' : false"}]},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"物流方式：",prop:"logistics"}},[e("el-checkbox-group",{on:{change:t.logisticsBtn},model:{value:t.formValidate.logistics,callback:function(e){t.$set(t.formValidate,"logistics",e)},expression:"formValidate.logistics"}},[e("el-checkbox",{attrs:{label:"1"}},[t._v("快递")]),e("el-checkbox",{attrs:{label:"2"}},[t._v("到店核销")])],1)],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"运费设置："}},[e("el-radio-group",{model:{value:t.formValidate.freight,callback:function(e){t.$set(t.formValidate,"freight",e)},expression:"formValidate.freight"}},[e("el-radio",{attrs:{label:2}},[t._v("固定邮费")]),e("el-radio",{attrs:{label:3}},[t._v("运费模板")])],1)],1)],1),3!=t.formValidate.freight&&1!=t.formValidate.freight?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"",prop:1!=t.formValidate.freight?"freight":""}},[e("div",{staticClass:"acea-row"},[e("el-input-number",{staticClass:"content_width maxW",attrs:{controls:!1,min:0,placeholder:"请输入金额"},model:{value:t.formValidate.postage,callback:function(e){t.$set(t.formValidate,"postage",e)},expression:"formValidate.postage"}})],1)])],1):t._e(),3==t.formValidate.freight?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"",prop:"temp_id"}},[e("div",{staticClass:"acea-row"},[e("el-select",{staticClass:"content_width maxW",attrs:{clearable:"",placeholder:"请选择运费模板"},model:{value:t.formValidate.temp_id,callback:function(e){t.$set(t.formValidate,"temp_id",e)},expression:"formValidate.temp_id"}},t._l(t.templateList,(function(t,a){return e("el-option",{key:a,attrs:{value:t.id,label:t.name}})})),1),e("span",{staticClass:"addfont",on:{click:t.addTemp}},[t._v("新增运费模板")])],1)])],1):t._e()],1),e("el-row",{directives:[{name:"show",rawName:"v-show",value:6===t.headTab.length?"5"===t.currentTab:"4"===t.currentTab,expression:"headTab.length === 6 ? currentTab === '5' : currentTab === '4'"}],attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"已售数量："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,placeholder:"请输入虚拟销量"},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"排序："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,placeholder:"请输入排序"},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),e("el-col",{attrs:{span:24}},[e("div",{staticClass:"line"})]),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"购买送积分：",prop:"give_integral"}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:0,max:9999999999,placeholder:"请输入积分"},model:{value:t.formValidate.give_integral,callback:function(e){t.$set(t.formValidate,"give_integral",e)},expression:"formValidate.give_integral"}})],1)],1),e("el-col",t._b({},"el-col",t.grid3,!1),[e("el-form-item",{attrs:{label:"购买送优惠券："}},[t.couponName.length?e("div",{staticClass:"mb20"},t._l(t.couponName,(function(a,i){return e("el-tag",{key:i,attrs:{closable:""},on:{close:function(e){return t.handleClose(a)}}},[t._v(t._s(a.title))])})),1):t._e(),e("el-button",{attrs:{type:"primary"},on:{click:t.addCoupon}},[t._v("添加优惠券")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"关联用户标签：",prop:"label_id"}},[e("div",{staticStyle:{display:"flex"}},[e("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openLabel}},[e("div",{staticStyle:{width:"90%"}},[t.dataLabel.length?e("div",t._l(t.dataLabel,(function(a,i){return e("el-tag",{key:i,attrs:{closable:""},on:{close:function(e){return t.closeLabel(a)}}},[t._v(t._s(a.label_name))])})),1):e("span",{staticClass:"span"},[t._v("选择用户关联标签")])]),e("div",{staticClass:"iconfont iconxiayi"})]),e("span",{staticClass:"addfont",on:{click:t.addLabel}},[t._v("新增标签")])])])],1),e("el-col",{attrs:{span:24}},[e("div",{staticClass:"line"})]),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"付费会员专属："}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"large"},model:{value:t.formValidate.vip_product,callback:function(e){t.$set(t.formValidate,"vip_product",e)},expression:"formValidate.vip_product"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),e("div",{staticClass:"titTip"},[t._v("开启后仅付费会员可以看见并购买此商品")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"单独设置："}},[e("el-checkbox-group",{on:{change:t.checkAllGroupChange},model:{value:t.formValidate.is_sub,callback:function(e){t.$set(t.formValidate,"is_sub",e)},expression:"formValidate.is_sub"}},[e("el-checkbox",{attrs:{label:1}},[t._v("佣金设置（数字即返佣金额）")]),e("el-checkbox",{attrs:{label:0}},[t._v("付费会员价")])],1)],1)],1),t.formValidate.is_sub.length?e("el-col",{attrs:{span:24}},[0===t.formValidate.spec_type?e("el-form-item",{attrs:{label:"商品属性："}},[e("el-table",{attrs:{data:t.oneFormValidate,border:""}},t._l(t.columnsInstall,(function(a,i){return e("el-table-column",{key:i,attrs:{label:a.title,"min-width":a.minWidth},scopedSlots:t._u([{key:"default",fn:function(i){return[a.key?[e("div",[e("span",[t._v(t._s(i.row[a.key]))])])]:"pic"===a.slot?[e("div",{staticClass:"pictrue pictrueTab"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.oneFormValidate[0].pic,expression:"oneFormValidate[0].pic"}]})])]:"price"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].price))])]:"cost"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].cost))])]:"ot_price"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].ot_price))])]:"stock"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].stock))])]:"bar_code"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].bar_code))])]:"weight"===a.slot?[e("span",[t._v(t._s(t.oneFormValidate[0].weight))])]:"fictitious"===a.slot?[t.row.coupon_id||2!=t.formValidate.virtual_type?t.row.coupon_id&&2==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.row,"manyFormValidate",i.$index)}}},[t._v(t._s(t.row.coupon_name))]):t.row.virtual_list.length||t.row.stock||1!=t.formValidate.virtual_type?(t.row.virtual_list.length||t.row.stock)&&1==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.row,"oneFormValidate",i.$index)}}},[t._v("已设置")]):t._e():e("el-button",{on:{click:function(e){return t.addVirtual(i.$index,"oneFormValidate")}}},[t._v("添加卡密")]):e("el-button",{on:{click:function(e){return t.addGoodsCoupon(i.$index,"oneFormValidate")}}},[t._v("添加优惠券")])]:"brokerage"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormValidate[0].brokerage,callback:function(e){t.$set(t.oneFormValidate[0],"brokerage",e)},expression:"oneFormValidate[0].brokerage"}})]:"brokerage_two"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormValidate[0].brokerage_two,callback:function(e){t.$set(t.oneFormValidate[0],"brokerage_two",e)},expression:"oneFormValidate[0].brokerage_two"}})]:"vip_price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormValidate[0].vip_price,callback:function(e){t.$set(t.oneFormValidate[0],"vip_price",e)},expression:"oneFormValidate[0].vip_price"}})]:t._e()]}}],null,!0)})})),1)],1):t._e(),1===t.formValidate.spec_type?e("el-form-item",{attrs:{label:"批量设置："}},[-1<t.formValidate.is_sub.indexOf(1)?e("span",[e("span",{staticClass:"brokerage"},[t._v("一级返佣：")]),e("el-input",{staticClass:"columnsBox content_width",attrs:{type:"number",controls:!1,placeholder:"请输入一级返佣"},model:{value:t.manyBrokerage,callback:function(e){t.manyBrokerage=e},expression:"manyBrokerage"}},[e("template",{slot:"append"},[t._v("%")])],2),e("span",{staticClass:"brokerage"},[t._v("二级返佣：")]),e("el-input",{staticClass:"columnsBox content_width",attrs:{type:"number",controls:!1,placeholder:"请输入二级返佣"},model:{value:t.manyBrokerageTwo,callback:function(e){t.manyBrokerageTwo=e},expression:"manyBrokerageTwo"}},[e("template",{slot:"append"},[t._v("%")])],2)],1):t._e(),-1<t.formValidate.is_sub.indexOf(0)?e("span",[t._v("\n                会员价："),e("el-input-number",{staticClass:"columnsBox content_width",attrs:{controls:!1,placeholder:"请输入会员价",min:0,max:9999999999},model:{value:t.manyVipPrice,callback:function(e){t.manyVipPrice=e},expression:"manyVipPrice"}})],1):t._e(),e("el-button",{attrs:{type:"primary"},on:{click:t.brokerageSetUp}},[t._v("批量设置")])],1):t._e(),1===t.formValidate.spec_type&&t.manyFormValidate.length?e("el-form-item",{attrs:{label:"商品属性："}},[t.formValidate.is_sub?e("el-table",{attrs:{data:t.manyFormValidate,border:""}},t._l(t.columnsInstal2,(function(a,i){return e("el-table-column",{key:i,attrs:{label:a.title,"min-width":a.minWidth},scopedSlots:t._u([{key:"default",fn:function(o){return[a.key?[e("div",[e("span",[t._v(t._s(o.row[a.key]))])])]:"pic"===a.slot?[e("div",{staticClass:"pictrue pictrueTab"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.manyFormValidate[o.$index].pic,expression:"manyFormValidate[scope.$index].pic"}]})])]:"price"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].price))])]:"cost"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].cost))])]:"ot_price"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].ot_price))])]:"stock"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].stock))])]:"bar_code"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].bar_code))])]:"weight"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[i].weight))])]:"fictitious"===a.slot?[t.row.coupon_id||2!=t.formValidate.virtual_type?t.row.coupon_id&&2==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.row,"manyFormValidate",o.$index)}}},[t._v(t._s(t.row.coupon_name))]):t.row.virtual_list.length||t.row.stock||1!=t.formValidate.virtual_type?(t.row.virtual_list.length||t.row.stock)&&1==t.formValidate.virtual_type?e("span",{staticClass:"see",on:{click:function(e){return t.see(t.row,"manyFormValidate",o.$index)}}},[t._v("已设置")]):t._e():e("el-button",{on:{click:function(e){return t.addVirtual(o.$index,"manyFormValidate")}}},[t._v("添加卡密")]):e("el-button",{on:{click:function(e){return t.addGoodsCoupon(o.$index,"manyFormValidate")}}},[t._v("添加优惠券")])]:"volume"===a.slot?[e("span",[t._v(t._s(t.manyFormValidate[o.$index].volume))])]:"brokerage"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[o.$index].brokerage,callback:function(e){t.$set(t.manyFormValidate[o.$index],"brokerage",e)},expression:"manyFormValidate[scope.$index].brokerage"}})]:"brokerage_two"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[o.$index].brokerage_two,callback:function(e){t.$set(t.manyFormValidate[o.$index],"brokerage_two",e)},expression:"manyFormValidate[scope.$index].brokerage_two"}})]:"vip_price"===a.slot?[e("el-input-number",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[o.$index].vip_price,callback:function(e){t.$set(t.manyFormValidate[o.$index],"vip_price",e)},expression:"manyFormValidate[scope.$index].vip_price"}})]:t._e()]}}],null,!0)})})),1):t._e()],1):t._e()],1):t._e(),e("el-col",{attrs:{span:24}},[e("div",{staticClass:"line"})]),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"起购数量："}},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,min:1,max:9999999999,precision:0,placeholder:"请输入起购数量"},model:{value:t.formValidate.min_qty,callback:function(e){t.$set(t.formValidate,"min_qty",e)},expression:"formValidate.min_qty"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"是否限购："}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"large"},model:{value:t.formValidate.is_limit,callback:function(e){t.$set(t.formValidate,"is_limit",e)},expression:"formValidate.is_limit"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1),e("el-col",{attrs:{span:24}},[t.formValidate.is_limit?e("el-form-item",{attrs:{label:"限购类型："}},[e("el-radio-group",{model:{value:t.formValidate.limit_type,callback:function(e){t.$set(t.formValidate,"limit_type",e)},expression:"formValidate.limit_type"}},[e("el-radio",{attrs:{label:1}},[t._v("单次限购")]),e("el-radio",{attrs:{label:2}},[t._v("单人限购")])],1),e("div",{staticClass:"titTip"},[t._v("单次限购是限制每次下单最多购买的数量，单人限购是限制一个用户总共可以购买的数量")])],1):t._e()],1),t.formValidate.is_limit?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"限购数量：",prop:"limit_num"}},[e("div",{staticClass:"acea-row row-middle"},[e("el-input-number",{staticClass:"content_width",attrs:{controls:!1,placeholder:"请输入限购数量",precision:0,min:1},model:{value:t.formValidate.limit_num,callback:function(e){t.$set(t.formValidate,"limit_num",e)},expression:"formValidate.limit_num"}}),e("span",{staticClass:"ml10"},[t._v(" 件 ")])],1)])],1):t._e(),0==t.formValidate.virtual_type||3==t.formValidate.virtual_type?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"预售商品："}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"large"},model:{value:t.formValidate.presale,callback:function(e){t.$set(t.formValidate,"presale",e)},expression:"formValidate.presale"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1):t._e(),t.formValidate.presale?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"预售活动时间：",prop:"presale_time"}},[e("div",{staticClass:"acea-row row-middle"},[e("el-date-picker",{attrs:{clearable:"",editable:!1,type:"datetimerange",format:"yyyy-MM-dd HH:mm","value-format":"yyyy-MM-dd HH:mm","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.onchangeTime},model:{value:t.formValidate.presale_time,callback:function(e){t.$set(t.formValidate,"presale_time",e)},expression:"formValidate.presale_time"}})],1),e("div",{staticClass:"titTip"},[t._v("设置活动开启结束时间，用户可以在设置时间内发起参与预售")])])],1):t._e(),t.formValidate.presale?e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"发货时间：",prop:"presale_day"}},[e("div",{staticClass:"acea-row row-middle"},[e("span",{staticClass:"mr10"},[t._v("预售活动结束后")]),e("el-input-number",{attrs:{controls:!1,placeholder:"请输入发货时间",precision:0,min:1},model:{value:t.formValidate.presale_day,callback:function(e){t.$set(t.formValidate,"presale_day",e)},expression:"formValidate.presale_day"}}),e("span",{staticClass:"ml10"},[t._v(" 天之内 ")]),e("div",{staticClass:"ml10 grey"})],1)])],1):t._e(),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品推荐："}},[e("el-checkbox-group",{model:{value:t.formValidate.recommend,callback:function(e){t.$set(t.formValidate,"recommend",e)},expression:"formValidate.recommend"}},[e("el-checkbox",{attrs:{label:"is_hot"}},[t._v("热卖单品")]),e("el-checkbox",{attrs:{label:"is_benefit"}},[t._v("促销单品")]),e("el-checkbox",{attrs:{label:"is_best"}},[t._v("精品推荐")]),e("el-checkbox",{attrs:{label:"is_new"}},[t._v("首发新品")]),e("el-checkbox",{attrs:{label:"is_good"}},[t._v("优品推荐")])],1)],1)],1),e("el-col",t._b({},"el-col",t.grid3,!1),[e("el-form-item",{attrs:{label:"活动优先级："}},[e("div",{staticClass:"color-list acea-row row-middle"},t._l(t.formValidate.activity,(function(a){return e("div",{directives:[{name:"dragging",rawName:"v-dragging",value:{item:a,list:t.formValidate.activity,group:"color"},expression:"{\n                    item: color,\n                    list: formValidate.activity,\n                    group: 'color',\n                  }"}],key:a,staticClass:"color-item",class:t.activity[a]},[t._v("\n                  "+t._s(a)+"\n                ")])})),0),e("div",{staticClass:"titTip"},[t._v("可拖动按钮调整活动的优先展示顺序")])])],1),e("el-col",t._b({},"el-col",t.grid3,!1),[e("el-form-item",{attrs:{label:"选择优品推荐商品："}},[e("div",{staticClass:"picBox"},[t._l(t.formValidate.recommend_list,(function(a,i){return e("div",{key:i,staticClass:"pictrue"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a.image,expression:"item.image"}]}),e("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemoveRecommend(i)}}})])})),e("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:t.changeGoods}},[e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})])],2)])],1)],1),e("el-row",{directives:[{name:"show",rawName:"v-show",value:6===t.headTab.length?"6"===t.currentTab:"5"===t.currentTab,expression:"headTab.length === 6 ? currentTab === '6' : currentTab === '5'"}],attrs:{justify:"space-between"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品关键字："}},[e("el-input",{staticClass:"content_width",attrs:{placeholder:"请输入商品关键字"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword","string"==typeof e?e.trim():e)},expression:"formValidate.keyword"}}),e("div",{staticClass:"titTip"},[t._v("PC端的SEO优化以及可以根据关键字进行商品搜索")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品简介："}},[e("el-input",{staticClass:"content_width",attrs:{type:"textarea",rows:3,placeholder:"请输入商品简介"},model:{value:t.formValidate.store_info,callback:function(e){t.$set(t.formValidate,"store_info","string"==typeof e?e.trim():e)},expression:"formValidate.store_info"}}),e("div",{staticClass:"titTip"},[t._v("公众号分享商品以及PC端SEO优化使用")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品口令："}},[e("el-input",{staticClass:"content_width",attrs:{placeholder:"请输入商品口令",type:"textarea",rows:3},model:{value:t.formValidate.command_word,callback:function(e){t.$set(t.formValidate,"command_word","string"==typeof e?e.trim():e)},expression:"formValidate.command_word"}}),e("div",{staticClass:"titTip"},[t._v("将其他平台的商品口令填写保存，移动端进入商品详情的时候自动复制")])],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"商品推荐图："}},[e("div",{staticClass:"pictrueBox",on:{click:function(e){return t.modalPicTap("dan","recommend_image")}}},[t.formValidate.recommend_image?e("div",{staticClass:"pictrue"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.recommend_image,expression:"formValidate.recommend_image"}]}),e("el-input",{staticStyle:{display:"none"},model:{value:t.formValidate.recommend_image,callback:function(e){t.$set(t.formValidate,"recommend_image","string"==typeof e?e.trim():e)},expression:"formValidate.recommend_image"}})],1):e("div",{staticClass:"upLoad acea-row row-center-wrapper"},[e("el-input",{staticStyle:{display:"none"},model:{value:t.formValidate.recommend_image,callback:function(e){t.$set(t.formValidate,"recommend_image","string"==typeof e?e.trim():e)},expression:"formValidate.recommend_image"}}),e("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"24px"}})],1),e("div",{staticClass:"titTip"},[t._v("移动端分类样式3显示的长方形图片，建议比例：5:2")])])])],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"自定义表单："}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"large"},on:{change:t.customMessBtn},model:{value:t.customBtn,callback:function(e){t.customBtn=e},expression:"customBtn"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),t.customBtn?e("div",{staticClass:"addCustom_content"},t._l(t.formValidate.custom_form,(function(a,i){return e("div",{key:i,staticClass:"custom_box"},[e("el-input",{staticStyle:{width:"150px","margin-right":"10px"},attrs:{placeholder:"表单标题"+(i+1),maxlength:10},model:{value:a.title,callback:function(e){t.$set(a,"title","string"==typeof e?e.trim():e)},expression:"item.title"}}),e("el-select",{staticStyle:{width:"200px","margin-left":"6px","margin-right":"10px"},model:{value:a.label,callback:function(e){t.$set(a,"label",e)},expression:"item.label"}},t._l(t.CustomList,(function(t){return e("el-option",{key:t.value,attrs:{value:t.value,label:t.label}})})),1),e("el-checkbox",{model:{value:a.status,callback:function(e){t.$set(a,"status",e)},expression:"item.status"}},[t._v("必填")]),e("div",{staticClass:"addfont",on:{click:function(e){return t.delcustom()}}},[t._v("删除")])],1)})),0):t._e(),e("div",{directives:[{name:"show",rawName:"v-show",value:t.customBtn,expression:"customBtn"}],staticClass:"addCustomBox"},[e("div",{staticClass:"btn",on:{click:t.addcustom}},[t._v("+ 添加表单")]),e("div",{staticClass:"titTip"},[t._v("用户下单时需填写的信息，最多可设置10条，设置了自定义表单的商品不能加入购物车")])])],1)],1)],1),e("el-form-item",["1"!==t.currentTab?e("el-button",{on:{click:t.upTab}},[t._v("上一步")]):t._e(),"6"!==t.currentTab&&0==t.formValidate.virtual_type?e("el-button",{staticClass:"submission",attrs:{type:"primary"},on:{click:t.downTab}},[t._v("下一步")]):t._e(),"5"!==t.currentTab&&0!=t.formValidate.virtual_type?e("el-button",{staticClass:"submission",attrs:{type:"primary"},on:{click:t.downTab}},[t._v("下一步")]):t._e(),!t.$route.params.id&&"6"!==t.currentTab||0!=t.formValidate.virtual_type?t._e():e("el-button",{staticClass:"submission",attrs:{type:"primary",disabled:t.openSubimit},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")]),!t.$route.params.id&&"5"!==t.currentTab||0==t.formValidate.virtual_type?t._e():e("el-button",{staticClass:"submission",attrs:{type:"primary",disabled:t.openSubimit},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")])],1)],1),e("el-dialog",{attrs:{visible:t.modalPic,width:"950px",scrollable:"",title:"上传商品图","close-on-click-modal":!1},on:{"update:visible":function(e){t.modalPic=e}}},[t.modalPic?e("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic,getPicD:t.getPicD}}):t._e()],1),e("el-dialog",{attrs:{visible:t.addVirtualModel,width:"720px",title:"添加卡密","show-close":!0,"close-on-click-modal":!1},on:{"update:visible":function(e){t.addVirtualModel=e},closed:t.initVirtualData}},[e("div",{staticClass:"trip"}),e("div",{staticClass:"type-radio"},[e("el-form",{attrs:{"label-width":"85px"}},[e("el-form-item",{attrs:{label:"卡密类型："}},[e("el-radio-group",{attrs:{size:"large"},model:{value:t.disk_type,callback:function(e){t.disk_type=e},expression:"disk_type"}},[e("el-radio",{attrs:{label:1}},[t._v("固定卡密")]),e("el-radio",{attrs:{label:2}},[t._v("一次性卡密")])],1),1==t.disk_type?e("div",[e("div",{staticClass:"stock-disk"},[e("el-input",{attrs:{size:"large",type:"textarea",rows:4,placeholder:"填写卡密信息"},model:{value:t.disk_info,callback:function(e){t.disk_info=e},expression:"disk_info"}})],1),e("div",{staticClass:"stock-input"},[e("el-input-number",{attrs:{controls:!1,max:1e5,min:1,step:1,precision:0},model:{value:t.stock,callback:function(e){t.stock=e},expression:"stock"}}),e("span",{staticClass:"pl10"},[t._v("件")])],1)]):t._e(),2==t.disk_type?e("div",{staticClass:"scroll-virtual"},t._l(t.virtualList,(function(a,i){return e("div",{key:i,staticClass:"virtual-data mb10"},[e("span",{staticClass:"mr10 virtual-title"},[t._v("卡号"+t._s(i+1)+"：")]),e("el-input",{staticClass:"mr10",staticStyle:{width:"150px"},attrs:{type:"text",placeholder:"请输入卡号(非必填)"},model:{value:a.key,callback:function(e){t.$set(a,"key","string"==typeof e?e.trim():e)},expression:"item.key"}}),e("span",{staticClass:"mr10 virtual-title"},[t._v("卡密"+t._s(i+1)+"：")]),e("el-input",{staticClass:"mr10",staticStyle:{width:"150px"},attrs:{type:"text",placeholder:"请输入卡密"},model:{value:a.value,callback:function(e){t.$set(a,"value","string"==typeof e?e.trim():e)},expression:"item.value"}}),e("span",{staticClass:"deteal-btn",on:{click:function(e){return t.removeVirtual(i)}}},[t._v("删除")])],1)})),0):t._e(),2==t.disk_type?e("div",{staticClass:"add-more"},[e("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增")]),e("el-upload",{staticClass:"ml10",attrs:{action:t.cardUrl,data:t.uploadData,headers:t.header,"on-success":t.upFile}},[e("el-button",[t._v("导入卡密")])],1)],1):t._e()],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.closeVirtual}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.upVirtual}},[t._v("确 定")])],1)])],1),e("freightTemplate",{ref:"templates",attrs:{template:t.template},on:{changeTemplate:t.changeTemplate}}),e("add-attr",{ref:"addattr",on:{getList:t.userSearchs}}),e("coupon-list",{ref:"couponTemplates",attrs:{couponids:t.formValidate.coupon_ids,updateIds:t.updateIds,updateName:t.updateName},on:{nameId:t.nameId}}),e("coupon-list",{ref:"goodsCoupon",attrs:{many:"one",luckDraw:!0},on:{getCouponId:t.goodsCouponId}}),e("el-dialog",{staticClass:"paymentFooter",attrs:{visible:t.goods_modals,title:"商品列表",footerHide:"",scrollable:"",width:"1000px"},on:{"update:visible":function(e){t.goods_modals=e}}},[t.goods_modals?e("goods-list",{ref:"goodslist",attrs:{ischeckbox:!0},on:{getProductId:t.getProductId}}):t._e()],1),e("el-dialog",{attrs:{visible:t.labelShow,title:"请选择用户标签","show-close":!0,width:"540px","close-on-click-modal":!1},on:{"update:visible":function(e){t.labelShow=e}}},[e("userLabel",{ref:"userLabel",on:{activeData:t.activeData,close:t.labelClose}})],1)],1)}),[],!1,null,"df43071e",null);e.default=_.exports}}]);