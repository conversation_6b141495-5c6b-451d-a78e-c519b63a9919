import LayoutMain from '@/layout';
import setting from '@/setting';
let routePre = setting.routePre;

const pre = 'customer_';

export default {
    path: routePre + '/customer',
    name: 'customer',
    header: 'customer',
    meta: {
        title: '客户管理1',
        // 授权标识
        auth: ['admin-customer-list'],
    },
    redirect: {
        name: `${pre}list`,
    },
    component: LayoutMain,
    children: [
        {
            path: 'list',
            name: `${pre}list`,
            meta: {
                title: '客户管理',
                auth: ['admin-customer-list'],
                keepAlive: true,
            },
            component: () => import('@/pages/customer/list/index'),
        },
        {
            path: 'level',
            name: `${pre}level`,
            meta: {
                auth: ['admin-customer-level'],
                footer: true,
                title: '客户等级',
            },
            component: () => import('@/pages/customer/level/index'),
        },
        {
            path: 'label',
            name: `${pre}label`,
            meta: {
                auth: ['admin-customer-label'],
                footer: true,
                title: '客户标签',
            },
            component: () => import('@/pages/customer/label/index'),
        },
        {
            path: 'vipcard/list',
            name: `${pre}vipcard_list`,
            meta: {
                auth: ['admin-customer-vipcard-list'],
                footer: true,
                title: '会员卡管理',
            },
            component: () => import('@/pages/customer/vipcard/index'),
        },
        {
            path: 'point/record',
            name: `${pre}point_record`,
            meta: {
                auth: ['admin-customer-point-record'],
                footer: true,
                title: '积分记录',
            },
            component: () => import('@/pages/customer/point/record'),
        },
        {
            path: 'point',
            name: `${pre}point`,
            meta: {
                auth: ['admin-customer-point'],
                footer: false,
                title: '积分查询',
                fullscreen: true
            },
            component: () => import('@/pages/customer/point/point'),
        },
        {
            path: 'point_type',
            name: `${pre}point_type`,
            meta: {
                auth: ['admin-customer-point_type'],
                footer: true,
                title: '积分项管理'
            },
            component: () => import('@/pages/customer/point/point_type'),
        },
    ],
};
