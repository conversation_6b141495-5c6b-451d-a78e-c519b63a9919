<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-button v-auth="['admin-token-token-add']" type="primary" @click="add"
      >添加TOKEN</el-button>
      <el-table
          :data="tokenLists"
          ref="table"
          class="mt14"
          v-loading="loading"
          highlight-current-row
          no-userFrom-text="暂无数据"
          no-filtered-userFrom-text="暂无筛选结果"
      >
        <el-table-column label="ID" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="TOKEN" min-width="180">
          <template slot-scope="scope">
            {{scope.row.title}}
          </template>
        </el-table-column>
        <el-table-column label="等级名称" min-width="100">
          <template slot-scope="scope">
            {{scope.row.userlevel.name}}
          </template>
        </el-table-column>
        <el-table-column label="所属门店" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.usergroup.group_name||'' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用人" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.user">{{ scope.row.user.nickname }}</span>
            <span v-else>暂未使用</span>
          </template>
        </el-table-column>

        <el-table-column label="使用时间" min-width="100">
          <template slot-scope="scope">
            <span>{{scope.row.update_time}}</span>
          </template>
        </el-table-column>
        <el-table-column label="添加时间" min-width="100">
          <template slot-scope="scope">
            <span>{{scope.row.add_time}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-dropdown size="small" @command="changeMenu(scope.row, $event, scope.$index)" :transfer="true">
              <span class="el-dropdown-link">更多<i class="el-icon-arrow-down el-icon--right"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <!--                                                <el-dropdown-item name="1">等级任务</el-dropdown-item>-->
                <el-dropdown-item command="2">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
            v-if="total"
            :total="total"
            :page.sync="levelFrom.page"
            :limit.sync="levelFrom.limit"
            @pagination="getTokenList"
        />
      </div>
    </el-card>
    <!-- 等级任务-->
    <task-list ref="tasks"></task-list>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex';
import { levelListApi, setShowApi} from '@/api/user';
import {getTokenListApi,createApi} from '@/api/token';
import taskList from './handle/task';
export default {
  name: 'tonken_list',
  components: { taskList },
  data() {
    return {
      grid: {
        xl: 7,
        lg: 7,
        md: 12,
        sm: 24,
        xs: 24,
      },
      loading: false,
      levelFrom: {
        is_show: '',
        title: '',
        page: 1,
        limit: 15,
      },
      levelLists: [],
      tokenLists:[],
      total: 0,
      FromData: null,
      imgName: '',
      visible: false,
      levelId: 0,
      modalTitleSs: '',
      titleType: 'level',
      modelTask: false,
      num: 0,
    };
  },
  created() {
   // this.getList();
    this.getTokenList();
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '80px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
  },
  methods: {
    ...mapMutations('userLevel', ['getlevelId']),
    getTokenList(){
      this.loading = true;
      getTokenListApi().then(res=>{
        let data = res.data;
        console.log(data);
        this.tokenLists = data.list;
        this.total = res.data.count;
        this.loading = false;

      }).catch(err=>{
        this.$message.error(err.msg);
      });
    },
    // 操作
    changeMenu(row, name, num) {
      this.levelId = row.id;
      switch (name) {
        case '1':
          this.getlevelId(this.levelId);
          this.$refs.tasks.modals = true;
          this.$refs.tasks.getList();
          break;
        case '3':
          this.edit(row);
          break;
        default:
          this.del(row, '删除Token', num);
      }
    },
    // 删除
    del(row, tit, num) {
      let delfromData = {
        title: tit,
        num: num,
        url: `token/token/delete/${row.id}`,
        method: 'put',
        ids: '',
      };
      this.$modalSure(delfromData)
          .then((res) => {
            this.$message.success(res.msg);
            this.levelLists.splice(num, 1);
            this.total--;
          })
          .catch((res) => {
            this.$message.error(res.msg);
          });
    },
    // 删除成功
    // submitModel () {
    //     this.levelLists.splice(this.delfromData.num, 1)
    // },

    // 添加
    add() {
      this.levelId = 0;
      this.$modalForm(createApi()).then(() => this.getList());
    },

  },
};
</script>

<style scoped lang="stylus">
.tabBox_img
  width 36px
  height 36px
  border-radius:4px
  cursor pointer
  img
    width 100%
    height 100%
</style>
