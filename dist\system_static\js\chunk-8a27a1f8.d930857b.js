(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-8a27a1f8"],{4565:function(t,e,n){"use strict";n.r(e),n("b0c0"),n("ac1f"),n("5319");var r=n("f6b0"),s={name:"customer_point",data:function(){return{searchForm:{cardNumber:""},customerInfo:{name:"",cardNumber:"",points:0},loading:!1,searchAttempted:!1,timer:null,focusTimer:null}},mounted:function(){this.focusInput(),document.addEventListener("click",this.focusInput),document.addEventListener("keydown",this.handleKeyDown),this.focusTimer=setInterval(this.focusInput,1e4)},beforeDestroy:function(){document.removeEventListener("click",this.focusInput),document.removeEventListener("keydown",this.handleKeyDown),this.focusTimer&&clearInterval(this.focusTimer),this.timer&&clearTimeout(this.timer)},methods:{goBack:function(){this.$router.go(-1)},handleKeyDown:function(t){"Escape"!==t.key&&27!==t.keyCode||this.goBack()},focusInput:function(){this.$nextTick((function(){var t=document.querySelector(".card-input input");t&&t.focus()}))},handleCardNumberInput:function(t){var e=this;this.timer&&clearTimeout(this.timer),this.searchForm.cardNumber=t.replace(/[^\d]/g,""),8===this.searchForm.cardNumber.length?this.timer=setTimeout((function(){e.searchCustomer()}),300):this.customerInfo={name:"",cardNumber:"",points:0}},searchCustomer:function(){var t=this;8!==this.searchForm.cardNumber.length?this.$message.warning("请输入8位会员卡号"):(this.loading=!0,this.searchAttempted=!0,Object(r.k)(this.searchForm.cardNumber).then((function(e){e.data?t.customerInfo={name:e.data.name||"",cardNumber:e.data.card_number||t.searchForm.cardNumber,points:e.data.points||0}:t.customerInfo={name:"",cardNumber:"",points:0},t.loading=!1,setTimeout((function(){t.searchForm.cardNumber="",t.focusInput()}),3e3)})).catch((function(e){t.customerInfo={name:"",cardNumber:"",points:0},t.loading=!1,t.$message.error(e.msg||"查询失败"),setTimeout((function(){t.searchForm.cardNumber="",t.focusInput()}),2e3)})))}}};n("cbff"),n=n("2877"),n=Object(n.a)(s,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"fullscreen-container"},[e("div",{staticClass:"back-button",attrs:{title:"按ESC键也可以返回"},on:{click:t.goBack}},[e("i",{staticClass:"el-icon-back"}),e("span",[t._v("返回")]),e("span",{staticClass:"shortcut-tip"},[t._v("(ESC)")])]),e("div",{staticClass:"query-container"},[t._m(0),e("div",{staticClass:"search-container"},[e("el-input",{staticClass:"card-input",attrs:{placeholder:"请输入8位会员卡号",clearable:"",maxlength:"8","prefix-icon":"el-icon-credit-card"},on:{input:t.handleCardNumberInput},model:{value:t.searchForm.cardNumber,callback:function(e){t.$set(t.searchForm,"cardNumber",e)},expression:"searchForm.cardNumber"}})],1),t.loading?e("div",{staticClass:"result-container loading-box"},[e("el-skeleton",{attrs:{rows:3,animated:""}})],1):t.customerInfo.name?e("div",{staticClass:"result-container"},[t._m(1),e("div",{staticClass:"customer-info"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("客户姓名")]),e("span",{staticClass:"value"},[t._v(t._s(t.customerInfo.name))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("会员卡号")]),e("span",{staticClass:"value"},[t._v(t._s(t.customerInfo.cardNumber))])]),e("div",{staticClass:"info-item points-item"},[e("span",{staticClass:"label"},[t._v("剩余积分")]),e("span",{staticClass:"value points"},[t._v(t._s(t.customerInfo.points))])])])]):t.searchAttempted&&!t.loading?e("div",{staticClass:"result-container no-result"},[t._m(2)]):t._e()])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"title-container"},[e("h1",{staticClass:"main-title"},[t._v("客户积分查询")]),e("p",{staticClass:"sub-title"},[t._v("输入会员卡号即可查询积分信息")])])},function(){var t=this._self._c;return t("div",{staticClass:"result-header"},[t("i",{staticClass:"el-icon-success"}),t("span",[this._v("查询成功")])])},function(){var t=this._self._c;return t("div",{staticClass:"result-header warning"},[t("i",{staticClass:"el-icon-warning"}),t("span",[this._v("未找到相关客户信息")])])}],!1,null,"0382521e",null);e.default=n.exports},cbff:function(t,e,n){"use strict";n("e220")},e220:function(t,e,n){},f6b0:function(t,e,n){"use strict";n.d(e,"j",(function(){return s})),n.d(e,"i",(function(){return c})),n.d(e,"h",(function(){return a})),n.d(e,"r",(function(){return u})),n.d(e,"c",(function(){return o})),n.d(e,"f",(function(){return i})),n.d(e,"q",(function(){return m})),n.d(e,"l",(function(){return d})),n.d(e,"k",(function(){return l})),n.d(e,"v",(function(){return f})),n.d(e,"d",(function(){return h})),n.d(e,"m",(function(){return p})),n.d(e,"a",(function(){return b})),n.d(e,"g",(function(){return v})),n.d(e,"e",(function(){return g})),n.d(e,"b",(function(){return C})),n.d(e,"n",(function(){return _})),n.d(e,"o",(function(){return j})),n.d(e,"t",(function(){return O})),n.d(e,"u",(function(){return k})),n.d(e,"p",(function(){return I})),n.d(e,"s",(function(){return N})),n("99af");var r=n("6b6c");function s(t){return Object(r.a)({url:"customer/getList",method:"get",params:t})}function c(t){return Object(r.a)({url:"customer/getCustomerLabelList",method:"get",params:t})}function a(t){return Object(r.a)({url:"customer/label/add",method:"get",params:{id:t}})}function u(){return Object(r.a)({url:"customer/level/list",method:"get"})}function o(t){return Object(r.a)({url:"customer/level/create/"+t,method:"get"})}function i(t){return Object(r.a)({url:"customer/info/".concat(t),method:"get"})}function m(t){return Object(r.a)({url:"customer/one_info",method:"get",params:t})}function d(t){return Object(r.a)({url:"customer/point_record",method:"get",params:t})}function l(t){return Object(r.a)({url:"customer/point/card_info",method:"get",params:{card_number:t}})}function f(t){return Object(r.a)({url:"customer/transfer",method:"post",data:t})}function h(t){return Object(r.a)({url:"customer/delete/".concat(t),method:"DELETE"})}function p(t){return Object(r.a)({url:"customer/point_type/list",method:"get",params:t})}function b(t){return Object(r.a)({url:"customer/point_type/add",method:"post",data:t})}function v(t,e){return Object(r.a)({url:"customer/point_type/edit/".concat(t),method:"post",data:e})}function g(t){return Object(r.a)({url:"customer/point_type/delete/".concat(t),method:"DELETE"})}function C(t,e){return Object(r.a)({url:"customer/point_type/status/".concat(t,"/").concat(e),method:"put"})}function _(t){return Object(r.a)({url:"customer/signCustomerList",method:"get",params:t})}function j(t){return Object(r.a)({url:"customer/getSignUserList",method:"get",params:t})}function O(t){return Object(r.a)({url:"customer/signCustomerSave",method:"post",data:t})}function k(t){return Object(r.a)({url:"customer/signCustomerUpdate",method:"post",data:t})}function I(t){return Object(r.a)({url:"customer/getSignCustomer",method:"get",params:t})}function N(t){return Object(r.a)({url:"customer/operateCustomerPoints",method:"post",data:t})}}}]);