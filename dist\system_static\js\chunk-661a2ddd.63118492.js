(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-661a2ddd"],{"1f5c":function(e,t,s){},3505:function(e,t,s){},"3a9a":function(e,t,s){},"640b":function(e,t,s){"use strict";s("66ab")},"66ab":function(e,t,s){},"73ac":function(e,t,s){"use strict";s("e47d")},"83e1":function(e,t,s){"use strict";s("f74f")},b625:function(e,t,s){"use strict";s("3a9a")},bda1:function(e,t,s){"use strict";s("3505")},bfbc:function(e,t,s){},e078:function(e,t,s){"use strict";s("bfbc")},e47d:function(e,t,s){},e770:function(e,t,s){"use strict";s.r(t),s("d3b7"),s("25f0");var a=s("2909"),i=s("c7eb"),r=s("1da1"),l=s("5530"),o=(s("d81d"),s("14d9"),s("a15b"),s("6062"),s("3ca3"),s("ddb0"),s("159b"),s("b64b"),s("e9c4"),s("99af"),s("232f")),n=s("2f62"),c=s("61f7"),d={name:"table-expand",filters:{formatDate:function(e){if(0!==e)return e=new Date(1e3*e),Object(c.a)(e,"yyyy-MM-dd hh:mm")}},props:{row:Object}},u=(s("efef"),s("2877")),m=(d=Object(u.a)(d,(function(){var e=this,t=e._self._c;return t("div",[t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("首次访问：")]),t("span",{staticClass:"expand-value"},[e._v(" "+e._s(e._f("formatDate")(e.row.add_time)))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("近次访问：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e._f("formatDate")(e.row.last_time)))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("身份证号：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.card_id))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("真实姓名：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.real_name))])])],1),t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("标签：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.labels))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("生日：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.birthday))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("推荐人：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.spread_uid_nickname))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("地址：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.addres))])])],1),t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("备注：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.mark))])])],1)],1)}),[],!1,null,"a5b498d0",null).exports,s("498a"),s("b0c0"),s("4de4"),s("a434"),s("c24f")),f={name:"userEdit",components:{userLabel:o.a},props:{userData:{type:Object,default:function(){}}},watch:{},data:function(){return{modals:!1,labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},mounted:function(){var e=this,t=(this.$set(this.infoData,"groupInfo",this.userData.groupInfo),this.$set(this.infoData,"levelInfo",this.userData.levelInfo),this.$set(this.infoData,"labelInfo",this.userData.labelInfo),Object.keys(this.formItem));this.userData.userInfo?(t.map((function(t){e.formItem[t]=e.userData.userInfo[t]})),this.formItem.birthday||(this.formItem.birthday=""),this.formItem.label_id.length&&(this.dataLabel=this.formItem.label_id)):this.reset()},methods:{addLabel:function(){this.$modalForm(Object(m.O)(0)).then((function(){}))},changeModal:function(e){e||(this.cancel(),this.reset())},openLabel:function(e){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},cancel:function(){},activeData:function(e){this.labelShow=!1,this.dataLabel=e},labelClose:function(){this.labelShow=!1},closeLabel:function(e){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id==e.id}))[0]);this.dataLabel.splice(t,1)},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}},p=(f=(s("b625"),Object(u.a)(f,(function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"formItem",attrs:{rules:e.ruleValidate,model:e.formItem,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[e.formItem.uid?t("el-form-item",{attrs:{label:"用户ID："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{disabled:"",placeholder:"请输入编号"},model:{value:e.formItem.uid,callback:function(t){e.$set(e.formItem,"uid",t)},expression:"formItem.uid"}})],1):e._e(),t("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入真实姓名"},model:{value:e.formItem.real_name,callback:function(t){e.$set(e.formItem,"real_name","string"==typeof t?t.trim():t)},expression:"formItem.real_name"}})],1),t("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入手机号码"},model:{value:e.formItem.phone,callback:function(t){e.$set(e.formItem,"phone",t)},expression:"formItem.phone"}})],1),t("el-form-item",{attrs:{label:"生日："}},[t("el-date-picker",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formItem.birthday,callback:function(t){e.$set(e.formItem,"birthday",t)},expression:"formItem.birthday"}})],1),t("el-form-item",{attrs:{label:"身份证号："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入身份证号"},model:{value:e.formItem.card_id,callback:function(t){e.$set(e.formItem,"card_id","string"==typeof t?t.trim():t)},expression:"formItem.card_id"}})],1),t("el-form-item",{attrs:{label:"用户地址："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户地址"},model:{value:e.formItem.addres,callback:function(t){e.$set(e.formItem,"addres",t)},expression:"formItem.addres"}})],1),t("el-form-item",{attrs:{label:"用户备注："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户备注"},model:{value:e.formItem.mark,callback:function(t){e.$set(e.formItem,"mark",t)},expression:"formItem.mark"}})],1),t("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.pwd,callback:function(t){e.$set(e.formItem,"pwd",t)},expression:"formItem.pwd"}})],1),t("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.true_pwd,callback:function(t){e.$set(e.formItem,"true_pwd",t)},expression:"formItem.true_pwd"}})],1),t("el-form-item",{attrs:{label:"用户等级："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.level,callback:function(t){e.$set(e.formItem,"level",t)},expression:"formItem.level"}},e._l(e.infoData.levelInfo,(function(e,s){return t("el-option",{key:s,attrs:{value:e.id,label:e.name}})})),1)],1),t("el-form-item",{attrs:{label:"用户分组："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.group_id,callback:function(t){e.$set(e.formItem,"group_id",t)},expression:"formItem.group_id"}},e._l(e.infoData.groupInfo,(function(e,s){return t("el-option",{key:s,attrs:{value:e.id,label:e.group_name}})})),1)],1),t("el-form-item",{attrs:{label:"用户标签："}},[t("div",{staticStyle:{display:"flex"}},[t("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:e.openLabel}},[t("div",{staticStyle:{width:"90%"}},[e.dataLabel.length?t("div",e._l(e.dataLabel,(function(s,a){return t("el-tag",{key:a,staticClass:"mr10",attrs:{closable:""},on:{close:function(t){return e.closeLabel(s)}}},[e._v(e._s(s.label_name))])})),1):t("span",{staticClass:"span"},[e._v("选择用户关联标签")])]),t("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),t("span",{staticClass:"addfont",on:{click:e.addLabel}},[e._v("新增标签")])])]),t("el-form-item",{attrs:{label:"推广资格："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.spread_open,callback:function(t){e.$set(e.formItem,"spread_open",t)},expression:"formItem.spread_open"}},[t("el-radio",{attrs:{label:1}},[e._v("启用")]),t("el-radio",{attrs:{label:0}},[e._v("禁用")])],1),t("div",{staticClass:"tip"},[e._v("禁用用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1),t("el-form-item",{attrs:{label:"推广权限："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.is_promoter,callback:function(t){e.$set(e.formItem,"is_promoter",t)},expression:"formItem.is_promoter"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1),t("div",{staticClass:"tip"},[e._v("指定分销模式下，开启或关闭用户的推广权限")])],1),t("el-form-item",{attrs:{label:"用户状态："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.status,callback:function(t){e.$set(e.formItem,"status",t)},expression:"formItem.status"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1)],1)],1),t("el-dialog",{attrs:{visible:e.labelShow,scrollable:"",title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{only_get:!0,uid:e.formItem.uid},on:{close:e.labelClose,activeData:e.activeData}}):e._e()],1)],1)}),[],!1,null,"b64a8ca6",null).exports),s("bbbc")),h=s("3f2a"),v=s("31b4"),b=s("a8e0"),_=s("5a0c"),g=s.n(_),w=(_={name:"userInfo",props:{psInfo:Object},filters:{timeFormat:function(e,t){return t=t?"YYYY-MM-DD":"YYYY-MM-DD HH:mm:ss",e?g()(1e3*e).format(t):"-"},gender:function(e){return 1==e?"男":2==e?"女":"未知"}},computed:{hasExtendInfo:function(){}}},_=(s("bda1"),Object(u.a)(_,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-info"},[t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("基本信息")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("用户ID：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.uid))])]),t("div",{staticClass:"item"},[t("div",[e._v("真实姓名：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.real_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("手机号码：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.phone||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("生日：")]),t("div",{staticClass:"value"},[e._v(e._s(e._f("timeFormat")(e.psInfo.birthday,"birthday")))])]),t("div",{staticClass:"item"},[t("div",[e._v("身份证号：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.card_id||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("用户地址：")]),t("div",{staticClass:"value"},[e._v(e._s("".concat(e.psInfo.addres)||"-"))])])])]),e._m(0),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户概况")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("推广资格：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.spread_open?"开启":"关闭"))])]),t("div",{staticClass:"item"},[t("div",[e._v("用户状态：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.status?"开启":"锁定"))])]),t("div",{staticClass:"item"},[t("div",[e._v("用户等级：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.vip_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("用户标签：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.label_list||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("用户分组：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.group_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("推广人：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.spread_uid_nickname||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("注册时间：")]),t("div",{staticClass:"value"},[e._v(e._s(e._f("timeFormat")(e.psInfo.add_time)))])]),t("div",{staticClass:"item"},[t("div",[e._v("登录时间：")]),t("div",{staticClass:"value"},[e._v(e._s(e._f("timeFormat")(e.psInfo.last_time)))])]),e.psInfo.is_money_level?t("div",{staticClass:"item"},[t("div",[e._v("付费会员：")]),t("div",{staticClass:"value"},[e._v("\n          "+e._s(1==e.psInfo.is_ever_level?"永久会员":e.psInfo.overdue_time?"".concat(e.psInfo.overdue_time," 到期"):"已过期")+"\n        ")])]):e._e()])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户备注")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("备注：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.mark||"-"))])])])])])}),[function(){var e=this._self._c;return e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[this._v("密码")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("div",[this._v("登录密码：")]),e("div",{staticClass:"value"},[this._v("********")])])])])}],!1,null,"c3058676",null).exports),s("a9e3"),{name:"userInfo",components:{userLabel:o.a},props:{userId:{type:Number,default:0}},filters:{timeFormat:function(e){return e?g()(1e3*e).format("YYYY-MM-DD HH:mm:ss"):"-"},gender:function(e){return 1==e?"男":2==e?"女":"未知"}},data:function(){return{labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},computed:{hasExtendInfo:function(){}},created:function(){this.getUserFrom(this.userId)},methods:{setUser:function(){var e=this,t=this.formItem,s=[];this.dataLabel.map((function(e){s.push(e.id)})),t.label_id=s,t.uid?Object(m.j)(t).then((function(t){e.$message.success(t.msg),e.$emit("success")})).catch((function(t){e.$message.error(t.msg)})):Object(m.H)(t).then((function(t){e.$emit("success"),e.$message.success(t.msg)})).catch((function(t){e.$message.error("err.msg")}))},addLabel:function(){this.$modalForm(Object(m.O)(0)).then((function(){}))},openLabel:function(e){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},closeLabel:function(e){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id==e.id}))[0]);this.dataLabel.splice(t,1)},getUserFrom:function(e){var t=this;Object(m.k)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){var a;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.userData=s.data,t.$set(t.infoData,"groupInfo",t.userData.groupInfo),t.$set(t.infoData,"levelInfo",t.userData.levelInfo),t.$set(t.infoData,"labelInfo",t.userData.labelInfo),a=Object.keys(t.formItem),t.userData.userInfo?(a.map((function(e){t.formItem[e]=t.userData.userInfo[e]})),t.formItem.birthday||(t.formItem.birthday=""),t.formItem.label_id.length&&(t.dataLabel=t.formItem.label_id)):t.reset();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error("res.msg")}))},labelClose:function(){this.labelShow=!1},activeData:function(e){this.labelShow=!1,this.dataLabel=e},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}}),y=(_=(s("640b"),{name:"userDetails",components:{userInfo:_,userEditForm:Object(u.a)(w,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-info"},[t("el-form",{ref:"formItem",attrs:{rules:e.ruleValidate,model:e.formItem,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("基本信息")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户ID："}},[t("el-input",{staticClass:"form-sty",attrs:{disabled:"",placeholder:"请输入编号"},model:{value:e.formItem.uid,callback:function(t){e.$set(e.formItem,"uid",t)},expression:"formItem.uid"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入真实姓名"},model:{value:e.formItem.real_name,callback:function(t){e.$set(e.formItem,"real_name","string"==typeof t?t.trim():t)},expression:"formItem.real_name"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入手机号码"},model:{value:e.formItem.phone,callback:function(t){e.$set(e.formItem,"phone",t)},expression:"formItem.phone"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"生日："}},[t("el-date-picker",{staticClass:"form-sty",attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formItem.birthday,callback:function(t){e.$set(e.formItem,"birthday",t)},expression:"formItem.birthday"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"身份证号："}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入身份证号"},model:{value:e.formItem.card_id,callback:function(t){e.$set(e.formItem,"card_id","string"==typeof t?t.trim():t)},expression:"formItem.card_id"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户地址："}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入用户地址"},model:{value:e.formItem.addres,callback:function(t){e.$set(e.formItem,"addres",t)},expression:"formItem.addres"}})],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("密码")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[t("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.pwd,callback:function(t){e.$set(e.formItem,"pwd",t)},expression:"formItem.pwd"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[t("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.true_pwd,callback:function(t){e.$set(e.formItem,"true_pwd",t)},expression:"formItem.true_pwd"}})],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户概况")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户等级："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.level,callback:function(t){e.$set(e.formItem,"level",t)},expression:"formItem.level"}},e._l(e.infoData.levelInfo,(function(e,s){return t("el-option",{key:s,attrs:{value:e.id,label:e.name}})})),1)],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户分组："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.group_id,callback:function(t){e.$set(e.formItem,"group_id",t)},expression:"formItem.group_id"}},e._l(e.infoData.groupInfo,(function(e,s){return t("el-option",{key:s,attrs:{value:e.id,label:e.group_name}})})),1)],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"用户标签："}},[t("div",{staticStyle:{display:"flex"}},[t("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:e.openLabel}},[t("div",{staticStyle:{width:"90%"}},[e.dataLabel.length?t("div",e._l(e.dataLabel,(function(s,a){return t("el-tag",{key:a,staticClass:"mr10",attrs:{closable:""},on:{close:function(t){return e.closeLabel(s)}}},[e._v(e._s(s.label_name))])})),1):t("span",{staticClass:"span"},[e._v("选择用户关联标签")])]),t("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),t("span",{staticClass:"addfont",on:{click:e.addLabel}},[e._v("新增标签")])])])],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"推广资格："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.spread_open,callback:function(t){e.$set(e.formItem,"spread_open",t)},expression:"formItem.spread_open"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("关闭")])],1),t("div",{staticClass:"tip"},[e._v("关闭用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"推广权限："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.is_promoter,callback:function(t){e.$set(e.formItem,"is_promoter",t)},expression:"formItem.is_promoter"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("关闭")]),t("div",{staticClass:"tip"},[e._v("指定分销模式下，开启或关闭用户的推广权限")])],1)],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"用户状态："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.status,callback:function(t){e.$set(e.formItem,"status",t)},expression:"formItem.status"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1)],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户备注")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户备注："}},[t("el-input",{staticClass:"form-sty",attrs:{type:"textarea",rows:5,placeholder:"请输入用户备注"},model:{value:e.formItem.mark,callback:function(t){e.$set(e.formItem,"mark",t)},expression:"formItem.mark"}})],1)],1)])])]),t("el-dialog",{attrs:{visible:e.labelShow,title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{only_get:!0,uid:e.formItem.uid},on:{close:e.labelClose,activeData:e.activeData}}):e._e()],1)],1)}),[],!1,null,"4fbe8108",null).exports},data:function(){return{isEdit:!1,theme2:"light",list:[{val:"order",label:"消费记录"},{val:"integral",label:"积分明细"},{val:"sign",label:"签到记录"},{val:"coupon",label:"持有优惠券"},{val:"balance_change",label:"余额变动"},{val:"spread",label:"好友关系"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"order",page:1,limit:20},total:0,columns:[],userLists:[],psInfo:{},activeName:"user"}},created:function(){},methods:{edit:function(){this.activeName="user",this.isEdit=!this.isEdit},editSave:function(){this.$refs.editForm.setUser()},draChange:function(){this.isEdit=!1},getDetails:function(e){var t=this;this.activeName="user",this.userId=e,this.spinShow=!0,this.isEdit=!1,Object(m.h)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){var a;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:200===s.status?(a=s.data,t.detailsData=a.headerList,t.psInfo=a.ps_info,t.spinShow=!1):(t.spinShow=!1,t.$message.error(s.msg));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.spinShow=!1,t.$message.error(e.msg)}))},changeTab:function(e){this.activeName=e.name,this.changeType()},changeType:function(){var e,t=this;this.loading=!0,this.userFrom.type=this.activeName,this.isEdit=!1,"user"!=this.activeName&&(""===this.userFrom.type&&(this.userFrom.type="order"),e={id:this.userId,datas:this.userFrom},Object(m.p)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:200!==s.status?e.next=19:(e.t0=t.userFrom.type,e.next="order"===e.t0?4:"integral"===e.t0?6:"sign"===e.t0?8:"coupon"===e.t0?10:"balance_change"===e.t0?12:14);break;case 4:return t.columns=[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],e.abrupt("break",15);case 6:return t.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",slot:"number",minWidth:120},{title:"变化后积分",key:"balance",minWidth:120},{title:"日期",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],e.abrupt("break",15);case 8:return t.columns=[{title:"动作",key:"title",minWidth:120},{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],e.abrupt("break",15);case 10:return t.columns=[{title:"优惠券名称",key:"coupon_title",minWidth:120},{title:"面值",key:"coupon_price",minWidth:120},{title:"有效期(天)",key:"coupon_time",minWidth:120},{title:"兑换时间",key:"_add_time",minWidth:120}],e.abrupt("break",15);case 12:return t.columns=[{title:"动作",key:"title",minWidth:120},{title:"余额变动",slot:"number",minWidth:120},{title:"当前余额",key:"balance",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],e.abrupt("break",15);case 14:t.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"type",minWidth:120},{title:"加入时间",key:"add_time",minWidth:120}];case 15:t.$nextTick((function(e){var a=s.data;t.userLists=a.list,t.total=a.count})),t.loading=!1,e.next=21;break;case 19:t.loading=!1,t.$message.error(s.msg);case 21:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)})))}}}),w=(s("e078"),s("83e1"),Object(u.a)(_,(function(){var e=this,t=e._self._c;return t("div",{staticStyle:{width:"100%"}},[t("el-drawer",{attrs:{visible:e.modals,title:"用户详情",wrapperClosable:!1,size:1100},on:{"update:visible":function(t){e.modals=t},closed:e.draChange}},[t("div",{staticClass:"acea-row head"},[t("div",{staticClass:"avatar mr15"},[t("img",{attrs:{src:e.psInfo.avatar}})]),t("div",{staticClass:"dashboard-workplace-header-tip"},[t("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:e._s(e.psInfo.nickname||"-")}}),t("div",{staticClass:"dashboard-workplace-header-tip-desc"},e._l(e.detailsData,(function(s,a){return t("span",{key:a,staticClass:"dashboard-workplace-header-tip-desc-sp"},[e._v(e._s(s.title+"："+s.value))])})),0)]),this.psInfo.is_del?e._e():t("div",{staticClass:"edit-btn"},[e.isEdit?e._e():t("el-button",{attrs:{type:"primary"},on:{click:e.edit}},[e._v("编辑")]),e.isEdit?t("el-button",{on:{click:e.edit}},[e._v("取消")]):e._e(),e.isEdit?t("el-button",{attrs:{type:"primary"},on:{click:e.editSave}},[e._v("保存")]):e._e()],1)]),t("el-row",{staticClass:"mt14",attrs:{justify:"space-between"}},[t("el-col",{attrs:{span:24}},[t("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.changeTab},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{name:"user",label:"用户信息"}},[e.isEdit?t("userEditForm",{ref:"editForm",attrs:{userId:e.userId},on:{success:function(t){return e.getDetails(e.userId)}}}):t("user-info",{attrs:{"ps-info":e.psInfo}})],1),e._l(e.list,(function(s,a){return t("el-tab-pane",{key:a,attrs:{name:s.val,label:s.label}},[[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",refInFor:!0,staticClass:"mt20",attrs:{data:e.userLists,"max-height":"400","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},e._l(e.columns,(function(s,a){return t("el-table-column",{key:a,attrs:{label:s.title,"min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[s.key?[t("div",[t("span",[e._v(e._s(a.row[s.key]))])])]:"number"===s.slot?[t("div",{class:a.row.pm?"plusColor":"reduceColor"},[e._v("\n                        "+e._s(a.row.pm?"+"+a.row.number:"-"+a.row.number)+"\n                      ")])]:e._e()]}}],null,!0)})})),1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.userFrom.page,limit:e.userFrom.limit},on:{"update:page":function(t){return e.$set(e.userFrom,"page",t)},"update:limit":function(t){return e.$set(e.userFrom,"limit",t)},pagination:e.changeType}}):e._e()],1)]],2)}))],2)],1)],1)],1)],1)}),[],!1,null,"38c56208",null).exports),_=s("c42b"),s("8c03")),I=s("b562");d={name:"user_list",components:{expandRow:d,editFrom:v.a,sendFrom:b.a,userDetails:w,newsCategory:_.a,customerInfo:y.default,userLabel:o.a,userEdit:f},data:function(){return{dataLabel:[],selectDataLabel:[],userData:{},modals:!1,selectLabelShow:!1,labelShow:!1,customerShow:!1,promoterShow:!1,labelActive:{uid:0},formInline:{uid:0,spread_uid:0,image:""},pickerOptions:this.$timeOptions,headeNum:[{type:"",name:"全部"},{type:"wechat",name:"微信公众号"},{type:"routine",name:"微信小程序"},{type:"h5",name:"H5"},{type:"pc",name:"PC"},{type:"app",name:"APP"}],address:[],addresData:[],isShowSend:!0,modal13:!1,maxCols:4,scrollerHeight:"600",contentTop:"130",contentWidth:"98%",grid:{xl:6,lg:6,md:8,sm:12,xs:24},grid2:{xl:8,lg:8,md:8,sm:12,xs:24},loading:!1,total:0,userFrom:{label_id:"",user_type:"",status:"",sex:"",is_promoter:"",country:"",isMember:"",pay_count:"",user_time_type:"",user_time:"",nickname:"",province:"",city:"",page:1,limit:15,level:"",group_id:"",field_key:""},field_key:"",level:"",group_id:"",label_id:"",user_time_type:"",pay_count:"",userLists:[],FromData:null,selectionList:[],user_ids:"",selectedData:[],timeVal:[],groupList:[],levelList:[],labelFrom:{page:1,limit:""},labelLists:[],selectedIds:[],ids:[]}},computed:Object(l.a)({},Object(n.d)("media",["isMobile"])),created:function(){this.getList(),this.getCityList()},mounted:function(){this.userGroup(),this.levelLists()},methods:{getCityList:function(){var e=this;Object(I.b)().then((function(t){e.addresData=t.data}))},setUser:function(){var e=this,t=this.$refs.userEdit.formItem,s=[];this.$refs.userEdit.dataLabel.map((function(e){s.push(e.id)})),t.label_id=s,t.uid?Object(m.j)(t).then((function(t){e.modals=!1,e.$message.success(t.msg),e.getList()})).catch((function(t){e.$message.error(t)})):Object(m.H)(t).then((function(t){e.modals=!1,e.$message.success(t.msg),e.getList()})).catch((function(t){e.$message.error(t.msg)}))},onceGetList:function(){this.labelActive.uid=0,this.getList()},labelClose:function(){this.labelActive.uid=0,this.labelShow=!1,this.selectLabelShow=!1},putSend:function(e){var t=this;this.$refs[e].validate((function(s){if(s){if(!t.formInline.spread_uid)return t.$message.error("请上传用户");Object(p.c)(t.formInline).then((function(s){t.promoterShow=!1,t.$message.success(s.msg),t.getList(),t.$refs[e].resetFields()})).catch((function(e){t.$message.error(e.msg)}))}}))},save:function(){this.modals=!0},synchro:function(){var e=this;Object(m.Z)().then((function(t){e.$message.success(t.msg)})).catch((function(t){e.$message.error(t.msg)}))},isSel:function(e){return!e.is_del},groupLists:function(){var e=this;this.loading=!0,Object(m.Q)(this.labelFrom).then(function(){var t=Object(r.a)(Object(i.a)().mark((function t(s){var a;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a=s.data,e.labelLists=a.list;case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},onClickTab:function(){this.userFrom.page=1,this.getList()},userGroup:function(){var e=this;Object(m.N)({page:1,limit:""}).then((function(t){e.groupList=t.data.list}))},levelLists:function(){var e=this;Object(m.r)({page:1,limit:"",title:"",is_show:1}).then((function(t){e.levelList=t.data.list}))},setGroup:function(){var e,t=this;0===this.ids.length?this.$message.warning("请选择要设置分组的用户"):(e={uids:this.ids},this.$modalForm(Object(m.Y)(e)).then((function(){return t.getList()})))},setLabel:function(){0===this.ids.length?this.$message.warning("请选择要设置标签的用户"):(this.ids,this.labelActive.uid=0,this.labelShow=!0)},activeSelectData:function(e){this.selectLabelShow=!1,this.selectDataLabel=e||[]},activeData:function(e){var t=this,s=[];e.length&&(e.map((function(e){s.push(e.id)})),Object(m.C)({uids:this.ids.join(","),label_id:s}).then((function(e){t.labelShow=!1,t.selectedIds=new Set,t.getList(),t.$message.success(e.msg)})))},changeMember:function(){this.userFrom.page=1,this.getList()},changeCountry:function(){"abroad"!==this.userFrom.country&&this.userFrom.country||(this.selectedData=[],this.userFrom.province="",this.userFrom.city="",this.address=[])},handleChange:function(e){this.selectedData=e.map((function(e){return e.label})),this.userFrom.province=this.selectedData[0],this.userFrom.city=this.selectedData[1]},onchangeTime:function(e){this.timeVal=e,this.userFrom.user_time=this.timeVal?this.timeVal.join("-"):""},userDetail:function(e){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(e.uid)},changeMenu:function(e,t,s){var a=this,i=[],r=(i.push(e.uid),{uids:i});switch(t){case"1":this.edit(e);break;case"2":this.getOtherFrom(e.uid);break;case"3":this.giveLevelTime(e.uid);break;case"4":this.del(e,"清除 【 "+this.tenText(e.nickname)+" 】的会员等级",s,"user");break;case"5":this.$modalForm(Object(m.Y)(r)).then((function(){return a.getList()}));break;case"6":this.openLabel(e);break;case"7":this.editS(e);break;default:this.del(e,"解除【 "+this.tenText(e.nickname)+" 】的上级推广人",s,"tuiguang")}},tenText:function(e){return 10<e.length?e.substr(0,10)+"...":e},openLabel:function(e){this.labelShow=!0,this.labelActive.uid=e.uid},openSelectLabel:function(){this.selectLabelShow=!0},editS:function(e){this.promoterShow=!0,this.formInline.uid=e.uid},customer:function(){this.customerShow=!0},imageObject:function(e){this.customerShow=!1,this.formInline.spread_uid=e.uid,this.formInline.image=e.image},cancel:function(e){this.promoterShow=!1,this.$refs[e].resetFields()},giveLevel:function(e){var t=this;this.$modalForm(Object(m.m)(e)).then((function(){return t.getList(1)}))},giveLevelTime:function(e){var t=this;this.$modalForm(Object(m.n)(e)).then((function(){return t.getList(1)}))},del:function(e,t,s,a){var i=this;t={title:t,num:s,url:("user"===a?"user/del_level/":"agent/stair/delete_spread/").concat(e.uid),method:"user"===a?"DELETE":"PUT",ids:"",width:600};this.$modalSure(t).then((function(e){i.$message.success(e.msg),i.getList()})).catch((function(e){i.$message.error(e.msg)}))},submitModel:function(){this.getList()},getList:function(){var e,t=this;this.selectDataLabel.length&&(e=[],this.selectDataLabel.forEach((function(t){e.push(t.id)})),this.userFrom.label_id=e.join(",")),this.userFrom.user_type=this.userFrom.user_type||"",this.userFrom.status=this.userFrom.status||"",this.userFrom.sex=this.userFrom.sex||"",this.userFrom.is_promoter=this.userFrom.is_promoter||"",this.userFrom.country=this.userFrom.country||"",this.userFrom.pay_count="all"===this.pay_count?"":this.pay_count,this.userFrom.user_time_type="all"===this.user_time_type?"":this.user_time_type,this.userFrom.field_key="all"===this.field_key?"":this.field_key,this.userFrom.level="all"===this.level?"":this.level,this.userFrom.group_id="all"===this.group_id?"":this.group_id,this.loading=!0,Object(m.T)(this.userFrom).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){var a;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=s.data,t.userLists=a.list,t.total=a.count,t.loading=!1,t.$nextTick((function(){t.setChecked()}));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))},exportList:function(){var e=this;return Object(r.a)(Object(i.a)().mark((function t(){var s,a,r,l,o,n,c,d;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.selectDataLabel.length&&(s=[],e.selectDataLabel.forEach((function(e){s.push(e.id)})),e.userFrom.label_id=s.join(",")),e.ids.length&&(e.userFrom.ids=e.ids),e.userFrom.user_type=e.userFrom.user_type||"",e.userFrom.status=e.userFrom.status||"",e.userFrom.sex=e.userFrom.sex||"",e.userFrom.is_promoter=e.userFrom.is_promoter||"",e.userFrom.country=e.userFrom.country||"",e.userFrom.pay_count="all"===e.pay_count?"":e.pay_count,e.userFrom.user_time_type="all"===e.user_time_type?"":e.user_time_type,e.userFrom.field_key="all"===e.field_key?"":e.field_key,e.userFrom.level="all"===e.level?"":e.level,e.userFrom.group_id="all"===e.group_id?"":e.group_id,a=[],r=[],l=[],o="",(n=JSON.parse(JSON.stringify(e.userFrom))).page=1,c=0;case 16:if(c<n.page+1)return t.next=19,e.getExcelData(n);t.next=33;break;case 19:d=t.sent,o=o||d.filename,r.length||(r=d.fileKey),a.length||(a=d.header),d.export.length?(l=l.concat(d.export),n.page++,t.next=30):t.next=28;break;case 28:return e.$exportExcel(a,r,o,l),t.abrupt("return");case 30:c++,t.next=16;break;case 33:case"end":return t.stop()}}),t)})))()},getExcelData:function(e){return new Promise((function(t,s){Object(h.c)(e).then((function(e){t(e.data)}))}))},pageChange:function(){this.selectionList=[],this.getList()},userSearchs:function(){this.userFrom.page=1,this.getList()},reset:function(e){this.userFrom={user_type:this.userFrom.user_type,status:"",sex:"",is_promoter:"",country:"",pay_count:"",user_time_type:"",user_time:"",nickname:"",field_key:"",level:"",group_id:"",label_id:"",page:1,limit:20},this.field_key="",this.level="",this.group_id="",this.dataLabel=[],this.selectDataLabel=[],this.user_time_type="",this.pay_count="",this.timeVal=[],this.selectedIds=new Set,this.getList()},getUserFrom:function(e){var t=this;Object(m.k)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.modals=!0,t.userData=s.data;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},getOtherFrom:function(e){var t=this;this.$modalForm(Object(m.i)(e)).then((function(){return t.getList(1)}))},onchangeIsShow:function(e){var t=this;e={id:e.uid,status:e.status};Object(m.q)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(s){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$message.success(s.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},onSend:function(){0===this.ids.length?this.$message.warning("请选择要发送优惠券的用户"):(this.$refs.sends.modals=!0,this.$refs.sends.getList())},onSendPic:function(){0===this.ids.length?this.$message.warning("请选择要发送图文消息的用户"):this.modal13=!0},edit:function(e){this.getUserFrom(e.uid)},submitFail:function(){},sortChanged:function(e,t,s){this.userFrom[e.prop]=e.order,this.getList()},handleSelectAll:function(e){var t=this,s=[];e.map((function(e){s.push(e.uid)})),this.selectedIds=s,this.$nextTick((function(){t.setChecked()}))},handleSelectRow:function(e,t){var s=this,a=[];e.map((function(e){a.push(e.uid)})),this.selectedIds=a,this.$nextTick((function(){s.setChecked()}))},setChecked:function(){this.ids=Object(a.a)(this.selectedIds);var e,t=this.$refs.table.objData;for(e in t)this.selectedIds.has(t[e].uid)&&(t[e]._isChecked=!0)}}},s("73ac"),v=Object(u.a)(d,(function(){var e=this,t=e._self._c;return t("div",[t("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never","body-style":{padding:0}}},[t("div",{staticClass:"padding-add"},[t("el-form",{ref:"userFrom",attrs:{model:e.userFrom,"label-width":"80px","label-position":"right",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"acea-row search-form"},[t("div",[t("el-form-item",{attrs:{label:"商户搜索：","label-for":"nickname"}},[t("el-input",{staticClass:"form_content_width",attrs:{placeholder:"请输入用户",clearable:""},model:{value:e.userFrom.nickname,callback:function(t){e.$set(e.userFrom,"nickname",t)},expression:"userFrom.nickname"}},[t("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:e.field_key,callback:function(t){e.field_key=t},expression:"field_key"}},[t("el-option",{attrs:{value:"all",label:"全部"}}),t("el-option",{attrs:{value:"uid",label:"UID"}}),t("el-option",{attrs:{value:"phone",label:"手机号"}}),t("el-option",{attrs:{value:"nickname",label:"用户昵称"}})],1)],1)],1)],1),t("el-form-item",{staticClass:"search-form-sub"},[t("el-button",{attrs:{type:"primary"},on:{click:e.userSearchs}},[e._v("搜索")])],1)],1)])],1)]),t("el-card",{staticClass:"ivu-mt mt16",attrs:{bordered:!1,shadow:"never","body-style":{padding:"20px 20px 20px"}}},[t("el-row",{attrs:{gutter:24,justify:"space-between"}},[t("el-col",{attrs:{span:24}},[t("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-user-save"],expression:"['admin-user-save']"}],attrs:{type:"primary"},on:{click:function(t){return e.edit({uid:0})}}},[e._v("添加商户")]),t("el-button",{staticClass:"mr10",on:{click:e.exportList}},[e._v("导出")])],1),e.selectionList.length?t("el-col",{staticClass:"userAlert",attrs:{span:24}},[t("el-alert",{attrs:{"show-icon":""}},[t("template",{slot:"title"},[e._v("\n            已选择"),t("i",{staticClass:"userI"},[e._v(" "+e._s(e.selectionList.length)+" ")]),e._v("项\n          ")])],2)],1):e._e()],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:e.userLists,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"sort-change":e.sortChanged,select:e.handleSelectRow,"select-all":e.handleSelectAll}},[t("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("expandRow",{attrs:{row:e.row}})]}}])}),t("el-table-column",{attrs:{type:"selection",selectable:e.isSel,width:"55"}}),t("el-table-column",{attrs:{label:"商户编号","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",[e._v(e._s(s.row.uid))])]}}])}),t("el-table-column",{attrs:{label:"店招","min-width":"60"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[t("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.row.avatar,expression:"scope.row.avatar"}]})])]}}])}),t("el-table-column",{attrs:{label:"店铺名称","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",{staticClass:"acea-row"},[t("i",{directives:[{name:"show",rawName:"v-show",value:"男"===s.row.sex,expression:"scope.row.sex === '男'"}],staticClass:"el-icon-male",staticStyle:{color:"#2db7f5","font-size":"15px"}}),t("i",{directives:[{name:"show",rawName:"v-show",value:"女"===s.row.sex,expression:"scope.row.sex === '女'"}],staticClass:"el-icon-female",staticStyle:{color:"#ed4014","font-size":"15px"}}),t("div",{domProps:{textContent:e._s(s.row.nickname)}})]),1==s.row.is_del?t("div",{staticStyle:{color:"red"}},[e._v("用户已注销")]):e._e()]}}])}),t("el-table-column",{attrs:{label:"付费会员","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.isMember?"是":"否"))])]}}])}),t("el-table-column",{attrs:{label:"用户等级","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.level))])]}}])}),t("el-table-column",{attrs:{label:"分组","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.group_id))])]}}])}),t("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.phone))])]}}])}),t("el-table-column",{attrs:{label:"用户类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.user_type))])]}}])}),t("el-table-column",{attrs:{label:"余额",prop:"now_money","min-width":"100",sortable:!0},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[e._v(e._s(s.row.now_money))])]}}])}),t("el-table-column",{attrs:{label:"操作",fixed:"right",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[1!=s.row.is_del?[t("a",{on:{click:function(t){return e.userDetail(s.row)}}},[e._v("详情")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-dropdown",{attrs:{size:"small",transfer:!0},on:{command:function(t){return e.changeMenu(s.row,t,s.$index)}}},[t("span",{staticClass:"el-dropdown-link"},[e._v("更多"),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"2"}},[e._v("积分余额")]),t("el-dropdown-item",{attrs:{command:"3"}},[e._v("赠送会员")]),t("el-dropdown-item",{attrs:{command:"5"}},[e._v("设置分组")]),t("el-dropdown-item",{attrs:{command:"6"}},[e._v("设置标签")]),t("el-dropdown-item",{attrs:{command:"7"}},[e._v("修改上级推广人")]),s.row.spread_uid?t("el-dropdown-item",{attrs:{command:"8"}},[e._v("清除上级推广人")]):e._e()],1)],1)]:[t("a",{on:{click:function(t){return e.userDetail(s.row)}}},[e._v("详情")])]]}}])})],1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.userFrom.page,limit:e.userFrom.limit},on:{"update:page":function(t){return e.$set(e.userFrom,"page",t)},"update:limit":function(t){return e.$set(e.userFrom,"limit",t)},pagination:e.pageChange}}):e._e()],1)],1),t("edit-from",{ref:"edits",attrs:{FromData:e.FromData},on:{submitFail:e.submitFail}}),t("send-from",{ref:"sends",attrs:{userIds:e.ids.toString()}}),t("user-details",{ref:"userDetails"}),t("el-dialog",{staticClass:"modelBox",attrs:{visible:e.modal13,title:"发送消息",width:"1200px"},on:{"update:visible":function(t){e.modal13=t}}},[e.modal13?t("news-category",{attrs:{isShowSend:e.isShowSend,userIds:e.ids.toString(),scrollerHeight:e.scrollerHeight,contentTop:e.contentTop,contentWidth:e.contentWidth,maxCols:e.maxCols}}):e._e()],1),t("el-dialog",{attrs:{visible:e.promoterShow,title:"修改推广人",width:"540px","show-close":!0},on:{"update:visible":function(t){e.promoterShow=t}}},[t("el-form",{ref:"formInline",attrs:{model:e.formInline,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[e.formInline?t("el-form-item",{attrs:{label:"选择推广人：",prop:"image"}},[t("div",{staticClass:"picBox",on:{click:e.customer}},[e.formInline.image?t("div",{staticClass:"pictrue"},[t("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.formInline.image,expression:"formInline.image"}]})]):t("div",{staticClass:"upLoad acea-row row-center-wrapper"},[t("i",{staticClass:"el-icon-user"})])])]):e._e()],1),t("div",{staticClass:"acea-row row-right mt20"},[t("el-button",{on:{click:function(t){return e.cancel("formInline")}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.putSend("formInline")}}},[e._v("提交")])],1)],1),t("el-dialog",{attrs:{visible:e.customerShow,title:"请选择商城用户","show-close":!0,width:"1000px"},on:{"update:visible":function(t){e.customerShow=t}}},[e.customerShow?t("customerInfo",{on:{imageObject:e.imageObject}}):e._e()],1),t("el-dialog",{attrs:{visible:e.labelShow,title:"请选择用户标签",width:"540px","show-close":!0},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{uid:e.labelActive.uid,only_get:!e.labelActive.uid},on:{close:e.labelClose,activeData:e.activeData,onceGetList:e.onceGetList}}):e._e()],1),t("el-drawer",{attrs:{visible:e.modals,wrapperClosable:!1,size:"720",title:"用户信息填写"},on:{"update:visible":function(t){e.modals=t}}},[e.modals?t("userEdit",{ref:"userEdit",attrs:{userData:e.userData}}):e._e(),t("div",{staticClass:"acea-row row-center"},[t("el-button",{on:{click:function(t){e.modals=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.setUser}},[e._v("提交")])],1)],1),t("el-dialog",{attrs:{visible:e.selectLabelShow,title:"请选择用户标签",width:"540px","show-close":!0,"close-on-click-modal":!1},on:{"update:visible":function(t){e.selectLabelShow=t}}},[e.selectLabelShow?t("userLabel",{ref:"userLabel",attrs:{uid:0,only_get:!0},on:{activeData:e.activeSelectData,close:e.labelClose}}):e._e()],1)],1)}),[],!1,null,"33c446db",null);t.default=v.exports},efef:function(e,t,s){"use strict";s("1f5c")},f74f:function(e,t,s){}}]);