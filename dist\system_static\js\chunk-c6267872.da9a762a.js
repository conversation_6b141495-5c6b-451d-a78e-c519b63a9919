(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-c6267872"],{"022c":function(t,e,s){},"0e813":function(t,e,s){"use strict";s("23f1")},"1adc":function(t,e,s){"use strict";s("cc13")},"23f1":function(t,e,s){},5592:function(t,e,s){"use strict";s("5a9f")},"5a9f":function(t,e,s){},"5ee8":function(t,e,s){},6195:function(t,e,s){"use strict";s("7775")},7775:function(t,e,s){},"7a55":function(t,e,s){"use strict";s("022c")},8407:function(t,e,s){"use strict";s("f386")},"87e1":function(t,e,s){"use strict";s("5ee8")},cc13:function(t,e,s){},f386:function(t,e,s){},fd1e:function(t,e,s){"use strict";s.r(e),s("b0c0"),s("d3b7"),s("25f0");var a=s("2909"),i=s("c7eb"),l=s("1da1"),r=s("5530"),o=(s("d81d"),s("14d9"),s("a434"),s("159b"),s("a15b"),s("6062"),s("3ca3"),s("ddb0"),s("b64b"),s("e9c4"),s("99af"),s("232f")),n=s("2f62"),c=s("61f7"),d={name:"table-expand",filters:{formatDate:function(t){if(0!==t)return t=new Date(1e3*t),Object(c.a)(t,"yyyy-MM-dd hh:mm")}},props:{row:Object}},u=(s("6195"),s("2877")),m=(d=Object(u.a)(d,(function(){var t=this,e=t._self._c;return e("div",[e("el-row",{staticClass:"expand-row"},[e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("首次访问：")]),e("span",{staticClass:"expand-value"},[t._v(" "+t._s(t._f("formatDate")(t.row.add_time)))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("近次访问：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t._f("formatDate")(t.row.last_time)))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("身份证号：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.card_id))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("真实姓名：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.real_name))])])],1),e("el-row",{staticClass:"expand-row"},[e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("标签：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.labels))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("生日：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.birthday))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("推荐人：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.spread_uid_nickname))])]),e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("地址：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.addres))])])],1),e("el-row",{staticClass:"expand-row"},[e("el-col",{attrs:{span:6}},[e("span",{staticClass:"expand-key"},[t._v("备注：")]),e("span",{staticClass:"expand-value"},[t._v(t._s(t.row.mark))])])],1)],1)}),[],!1,null,"cd1d5ce2",null).exports,s("498a"),s("4de4"),s("c24f")),f={name:"userEdit",components:{userLabel:o.a},props:{userData:{type:Object,default:function(){}}},watch:{},data:function(){return{modals:!1,labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},mounted:function(){var t=this,e=(this.$set(this.infoData,"groupInfo",this.userData.groupInfo),this.$set(this.infoData,"levelInfo",this.userData.levelInfo),this.$set(this.infoData,"labelInfo",this.userData.labelInfo),Object.keys(this.formItem));this.userData.userInfo?(e.map((function(e){t.formItem[e]=t.userData.userInfo[e]})),this.formItem.birthday||(this.formItem.birthday=""),this.formItem.label_id.length&&(this.dataLabel=this.formItem.label_id)):this.reset()},methods:{addLabel:function(){this.$modalForm(Object(m.M)(0)).then((function(){}))},changeModal:function(t){t||(this.cancel(),this.reset())},openLabel:function(t){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},cancel:function(){},activeData:function(t){this.labelShow=!1,this.dataLabel=t},labelClose:function(){this.labelShow=!1},closeLabel:function(t){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id==t.id}))[0]);this.dataLabel.splice(e,1)},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}},p=(f=(s("1adc"),Object(u.a)(f,(function(){var t=this,e=t._self._c;return e("div",[e("el-form",{ref:"formItem",attrs:{rules:t.ruleValidate,model:t.formItem,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[t.formItem.uid?e("el-form-item",{attrs:{label:"用户ID："}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{disabled:"",placeholder:"请输入编号"},model:{value:t.formItem.uid,callback:function(e){t.$set(t.formItem,"uid",e)},expression:"formItem.uid"}})],1):t._e(),e("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入真实姓名"},model:{value:t.formItem.real_name,callback:function(e){t.$set(t.formItem,"real_name","string"==typeof e?e.trim():e)},expression:"formItem.real_name"}})],1),e("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入手机号码"},model:{value:t.formItem.phone,callback:function(e){t.$set(t.formItem,"phone",e)},expression:"formItem.phone"}})],1),e("el-form-item",{attrs:{label:"生日："}},[e("el-date-picker",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.formItem.birthday,callback:function(e){t.$set(t.formItem,"birthday",e)},expression:"formItem.birthday"}})],1),e("el-form-item",{attrs:{label:"身份证号："}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入身份证号"},model:{value:t.formItem.card_id,callback:function(e){t.$set(t.formItem,"card_id","string"==typeof e?e.trim():e)},expression:"formItem.card_id"}})],1),e("el-form-item",{attrs:{label:"用户地址："}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户地址"},model:{value:t.formItem.addres,callback:function(e){t.$set(t.formItem,"addres",e)},expression:"formItem.addres"}})],1),e("el-form-item",{attrs:{label:"用户备注："}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户备注"},model:{value:t.formItem.mark,callback:function(e){t.$set(t.formItem,"mark",e)},expression:"formItem.mark"}})],1),e("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:t.formItem.pwd,callback:function(e){t.$set(t.formItem,"pwd",e)},expression:"formItem.pwd"}})],1),e("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[e("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:t.formItem.true_pwd,callback:function(e){t.$set(t.formItem,"true_pwd",e)},expression:"formItem.true_pwd"}})],1),e("el-form-item",{attrs:{label:"用户等级："}},[e("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:t.formItem.level,callback:function(e){t.$set(t.formItem,"level",e)},expression:"formItem.level"}},t._l(t.infoData.levelInfo,(function(t,s){return e("el-option",{key:s,attrs:{value:t.id,label:t.name}})})),1)],1),e("el-form-item",{attrs:{label:"用户分组："}},[e("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:t.formItem.group_id,callback:function(e){t.$set(t.formItem,"group_id",e)},expression:"formItem.group_id"}},t._l(t.infoData.groupInfo,(function(t,s){return e("el-option",{key:s,attrs:{value:t.id,label:t.group_name}})})),1)],1),e("el-form-item",{attrs:{label:"用户标签："}},[e("div",{staticStyle:{display:"flex"}},[e("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openLabel}},[e("div",{staticStyle:{width:"90%"}},[t.dataLabel.length?e("div",t._l(t.dataLabel,(function(s,a){return e("el-tag",{key:a,staticClass:"mr10",attrs:{closable:""},on:{close:function(e){return t.closeLabel(s)}}},[t._v(t._s(s.label_name))])})),1):e("span",{staticClass:"span"},[t._v("选择用户关联标签")])]),e("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),e("span",{staticClass:"addfont",on:{click:t.addLabel}},[t._v("新增标签")])])]),e("el-form-item",{attrs:{label:"推广资格："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.spread_open,callback:function(e){t.$set(t.formItem,"spread_open",e)},expression:"formItem.spread_open"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1),e("div",{staticClass:"tip"},[t._v("禁用用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1),e("el-form-item",{attrs:{label:"推广权限："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.is_promoter,callback:function(e){t.$set(t.formItem,"is_promoter",e)},expression:"formItem.is_promoter"}},[e("el-radio",{attrs:{label:1}},[t._v("开启")]),e("el-radio",{attrs:{label:0}},[t._v("锁定")])],1),e("div",{staticClass:"tip"},[t._v("指定分销模式下，开启或关闭用户的推广权限")])],1),e("el-form-item",{attrs:{label:"用户状态："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.status,callback:function(e){t.$set(t.formItem,"status",e)},expression:"formItem.status"}},[e("el-radio",{attrs:{label:1}},[t._v("开启")]),e("el-radio",{attrs:{label:0}},[t._v("锁定")])],1)],1)],1),e("el-dialog",{attrs:{visible:t.labelShow,scrollable:"",title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(e){t.labelShow=e}}},[t.labelShow?e("userLabel",{attrs:{only_get:!0,uid:t.formItem.uid},on:{close:t.labelClose,activeData:t.activeData}}):t._e()],1)],1)}),[],!1,null,"2d24d0a4",null).exports),s("f6b0")),v=s("bbbc"),h=s("3f2a"),b=s("31b4"),_=s("a8e0"),g=s("5a0c"),w=s.n(g),C=(g={name:"userInfo",props:{psInfo:Object},filters:{timeFormat:function(t,e){return e=e?"YYYY-MM-DD":"YYYY-MM-DD HH:mm:ss",t?w()(1e3*t).format(e):"-"},gender:function(t){return 1==t?"男":2==t?"女":"未知"}},computed:{hasExtendInfo:function(){}}},g=(s("5592"),Object(u.a)(g,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"user-info"},[e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("基本信息")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("div",[t._v("客户ID：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.id))])]),e("div",{staticClass:"item"},[e("div",[t._v("姓名：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.name||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("手机号码：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.tel||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("生日：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.birthday))])])])]),e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("客户概况")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("div",[t._v("客户等级：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.level_name||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("客户标签：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.tag_name||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("所属员工：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.parent_name||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("添加时间：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.add_time))])]),e("div",{staticClass:"item"},[e("div",[t._v("客户类别：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.lb||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("工作单位：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.gzdw||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("收入情况：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.sr||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("支出情况：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.zhichu||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("消费能力：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xfnl||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("消费习惯：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xfxg||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("需求：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xuqiu||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("健康意识：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.jkys||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("话事权：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.hsq||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("性格：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xg||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("竞品：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.jp||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("近期消费：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.jqxf||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("消费金额：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xfje||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("最近维护时间：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.zjwh||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("维护方式：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.whfs||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("转介力：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.zjl||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("心脑血管疾病：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.xnx?"未知":0==t.psInfo.xnx?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("高血压：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.gxy?"未知":0==t.psInfo.gxy?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("糖尿病：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.tnb?"未知":0==t.psInfo.tnb?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("静脉曲张：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.jmqz?"未知":0==t.psInfo.jmqz?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("骨病：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.gb?"未知":0==t.psInfo.gb?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("脑梗或心梗：")]),e("div",{staticClass:"value"},[t._v(t._s(-1==t.psInfo.ng?"未知":0==t.psInfo.ng?"无":"有"))])]),e("div",{staticClass:"item"},[e("div",[t._v("兴趣爱好：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.xqah||"-"))])]),e("div",{staticClass:"item"},[e("div",[t._v("住址：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.address||"-"))])])])]),e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("用户备注")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("div",[t._v("备注：")]),e("div",{staticClass:"value"},[t._v(t._s(t.psInfo.beizhu||"-"))])])])])])}),[],!1,null,"5af87abb",null).exports),s("a9e3"),{name:"userInfo",components:{userLabel:o.a},props:{userId:{type:Number,default:0}},filters:{timeFormat:function(t){return t?w()(1e3*t).format("YYYY-MM-DD HH:mm:ss"):"-"},gender:function(t){return 1==t?"男":2==t?"女":"未知"}},data:function(){return{labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},computed:{hasExtendInfo:function(){}},created:function(){this.getUserFrom(this.userId)},methods:{setUser:function(){var t=this,e=this.formItem,s=[];this.dataLabel.map((function(t){s.push(t.id)})),e.label_id=s,e.uid?Object(m.h)(e).then((function(e){t.$message.success(e.msg),t.$emit("success")})).catch((function(e){t.$message.error(e.msg)})):Object(m.F)(e).then((function(e){t.$emit("success"),t.$message.success(e.msg)})).catch((function(e){t.$message.error("err.msg")}))},addLabel:function(){this.$modalForm(Object(m.M)(0)).then((function(){}))},openLabel:function(t){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},closeLabel:function(t){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id==t.id}))[0]);this.dataLabel.splice(e,1)},getUserFrom:function(t){var e=this;Object(m.i)(t).then(function(){var t=Object(l.a)(Object(i.a)().mark((function t(s){var a;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.userData=s.data,e.$set(e.infoData,"groupInfo",e.userData.groupInfo),e.$set(e.infoData,"levelInfo",e.userData.levelInfo),e.$set(e.infoData,"labelInfo",e.userData.labelInfo),a=Object.keys(e.formItem),e.userData.userInfo?(a.map((function(t){e.formItem[t]=e.userData.userInfo[t]})),e.formItem.birthday||(e.formItem.birthday=""),e.formItem.label_id.length&&(e.dataLabel=e.formItem.label_id)):e.reset();case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$message.error("res.msg")}))},labelClose:function(){this.labelShow=!1},activeData:function(t){this.labelShow=!1,this.dataLabel=t},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}}),y=(g=(s("87e1"),{name:"userDetails",components:{userInfo:g,userEditForm:Object(u.a)(C,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"user-info"},[e("el-form",{ref:"formItem",attrs:{rules:t.ruleValidate,model:t.formItem,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("基本信息")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"用户ID："}},[e("el-input",{staticClass:"form-sty",attrs:{disabled:"",placeholder:"请输入编号"},model:{value:t.formItem.uid,callback:function(e){t.$set(t.formItem,"uid",e)},expression:"formItem.uid"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[e("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入真实姓名"},model:{value:t.formItem.real_name,callback:function(e){t.$set(t.formItem,"real_name","string"==typeof e?e.trim():e)},expression:"formItem.real_name"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[e("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入手机号码"},model:{value:t.formItem.phone,callback:function(e){t.$set(t.formItem,"phone",e)},expression:"formItem.phone"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"生日："}},[e("el-date-picker",{staticClass:"form-sty",attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.formItem.birthday,callback:function(e){t.$set(t.formItem,"birthday",e)},expression:"formItem.birthday"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"身份证号："}},[e("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入身份证号"},model:{value:t.formItem.card_id,callback:function(e){t.$set(t.formItem,"card_id","string"==typeof e?e.trim():e)},expression:"formItem.card_id"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"用户地址："}},[e("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入用户地址"},model:{value:t.formItem.addres,callback:function(e){t.$set(t.formItem,"addres",e)},expression:"formItem.addres"}})],1)],1)])]),e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("密码")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[e("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:t.formItem.pwd,callback:function(e){t.$set(t.formItem,"pwd",e)},expression:"formItem.pwd"}})],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[e("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:t.formItem.true_pwd,callback:function(e){t.$set(t.formItem,"true_pwd",e)},expression:"formItem.true_pwd"}})],1)],1)])]),e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("用户概况")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"用户等级："}},[e("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:t.formItem.level,callback:function(e){t.$set(t.formItem,"level",e)},expression:"formItem.level"}},t._l(t.infoData.levelInfo,(function(t,s){return e("el-option",{key:s,attrs:{value:t.id,label:t.name}})})),1)],1)],1),e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"用户分组："}},[e("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:t.formItem.group_id,callback:function(e){t.$set(t.formItem,"group_id",e)},expression:"formItem.group_id"}},t._l(t.infoData.groupInfo,(function(t,s){return e("el-option",{key:s,attrs:{value:t.id,label:t.group_name}})})),1)],1)],1),e("div",{staticClass:"item lang"},[e("el-form-item",{attrs:{label:"用户标签："}},[e("div",{staticStyle:{display:"flex"}},[e("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openLabel}},[e("div",{staticStyle:{width:"90%"}},[t.dataLabel.length?e("div",t._l(t.dataLabel,(function(s,a){return e("el-tag",{key:a,staticClass:"mr10",attrs:{closable:""},on:{close:function(e){return t.closeLabel(s)}}},[t._v(t._s(s.label_name))])})),1):e("span",{staticClass:"span"},[t._v("选择用户关联标签")])]),e("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),e("span",{staticClass:"addfont",on:{click:t.addLabel}},[t._v("新增标签")])])])],1),e("div",{staticClass:"item lang"},[e("el-form-item",{attrs:{label:"推广资格："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.spread_open,callback:function(e){t.$set(t.formItem,"spread_open",e)},expression:"formItem.spread_open"}},[e("el-radio",{attrs:{label:1}},[t._v("开启")]),e("el-radio",{attrs:{label:0}},[t._v("关闭")])],1),e("div",{staticClass:"tip"},[t._v("关闭用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1)],1),e("div",{staticClass:"item lang"},[e("el-form-item",{attrs:{label:"推广权限："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.is_promoter,callback:function(e){t.$set(t.formItem,"is_promoter",e)},expression:"formItem.is_promoter"}},[e("el-radio",{attrs:{label:1}},[t._v("开启")]),e("el-radio",{attrs:{label:0}},[t._v("关闭")]),e("div",{staticClass:"tip"},[t._v("指定分销模式下，开启或关闭用户的推广权限")])],1)],1)],1),e("div",{staticClass:"item lang"},[e("el-form-item",{attrs:{label:"用户状态："}},[e("el-radio-group",{staticClass:"form-sty",model:{value:t.formItem.status,callback:function(e){t.$set(t.formItem,"status",e)},expression:"formItem.status"}},[e("el-radio",{attrs:{label:1}},[t._v("开启")]),e("el-radio",{attrs:{label:0}},[t._v("锁定")])],1)],1)],1)])]),e("div",{staticClass:"section"},[e("div",{staticClass:"section-hd"},[t._v("用户备注")]),e("div",{staticClass:"section-bd"},[e("div",{staticClass:"item"},[e("el-form-item",{attrs:{label:"用户备注："}},[e("el-input",{staticClass:"form-sty",attrs:{type:"textarea",rows:5,placeholder:"请输入用户备注"},model:{value:t.formItem.mark,callback:function(e){t.$set(t.formItem,"mark",e)},expression:"formItem.mark"}})],1)],1)])])]),e("el-dialog",{attrs:{visible:t.labelShow,title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(e){t.labelShow=e}}},[t.labelShow?e("userLabel",{attrs:{only_get:!0,uid:t.formItem.uid},on:{close:t.labelClose,activeData:t.activeData}}):t._e()],1)],1)}),[],!1,null,"9e93a71a",null).exports},data:function(){return{isEdit:!1,theme2:"light",list:[{val:"0",label:"打电话记录"},{val:"1",label:"到店学习"},{val:2,label:"参加会议"},{val:3,label:"购买记录"},{val:4,label:"家访记录"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"order",page:1,limit:20},total:0,columns:[],userLists:[],psInfo:{},activeName:"user"}},created:function(){},methods:{edit:function(){this.activeName="user",this.isEdit=!this.isEdit},editSave:function(){this.$refs.editForm.setUser()},draChange:function(){this.isEdit=!1},getDetails:function(t){var e=this;this.activeName="user",this.userId=t,this.spinShow=!0,this.isEdit=!1,Object(p.b)(t).then(function(){var t=Object(l.a)(Object(i.a)().mark((function t(s){var a;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:200===s.status?(a=s.data,e.detailsData=a.headerList,e.psInfo=a.ps_info,e.spinShow=!1):(e.spinShow=!1,e.$message.error(s.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$message.error(t.msg)}))},changeTab:function(t){this.activeName=t.name,this.changeType()},changeType:function(){var t=this,e=(this.loading=!0,this.userFrom.type=this.activeName,this.isEdit=!1,{id:this.userId,type:this.userFrom.type});Object(p.l)(e).then(function(){var e=Object(l.a)(Object(i.a)().mark((function e(s){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:200!==s.status?e.next=19:(e.t0=t.userFrom.type,e.next="0"===e.t0?4:"1"===e.t0?6:"2"===e.t0?8:"3"===e.t0?10:"4"===e.t0?12:14);break;case 4:case 6:case 8:case 10:case 12:return t.columns=[{title:"ID",key:"id",minWidth:160},{title:"内容",key:"content",minWidth:250},{title:"时间",key:"add_time",minWidth:90}],e.abrupt("break",15);case 14:t.columns=[{title:"ID",key:"id",minWidth:160},{title:"内容",key:"content",minWidth:250},{title:"时间",key:"add_time",minWidth:90}];case 15:t.$nextTick((function(e){var a=s.data;t.userLists=a.list,t.total=a.count})),t.loading=!1,e.next=21;break;case 19:t.loading=!1,t.$message.error(s.msg);case 21:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))}}}),C=(s("8407"),s("0e813"),Object(u.a)(g,(function(){var t=this,e=t._self._c;return e("div",{staticStyle:{width:"100%"}},[e("el-drawer",{attrs:{visible:t.modals,title:"用户详情",wrapperClosable:!1,size:1100},on:{"update:visible":function(e){t.modals=e},closed:t.draChange}},[e("div",{staticClass:"acea-row head"},[e("div",{staticClass:"avatar mr15"},[e("img",{attrs:{src:t.psInfo.avatar}})]),e("div",{staticClass:"dashboard-workplace-header-tip"},[e("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:t._s(t.psInfo.name||"-")}}),e("div",{staticClass:"dashboard-workplace-header-tip-desc"},t._l(t.detailsData,(function(s,a){return e("span",{key:a,staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v(t._s(s.title+"："+s.value))])})),0)])]),e("el-row",{staticClass:"mt14",attrs:{justify:"space-between"}},[e("el-col",{attrs:{span:24}},[e("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.changeTab},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{name:"user",label:"用户信息"}},[t.isEdit?e("userEditForm",{ref:"editForm",attrs:{userId:t.userId},on:{success:function(e){return t.getDetails(t.userId)}}}):e("user-info",{attrs:{"ps-info":t.psInfo}})],1),t._l(t.list,(function(s,a){return e("el-tab-pane",{key:a,attrs:{name:s.val,label:s.label}},[[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",refInFor:!0,staticClass:"mt20",attrs:{data:t.userLists,"max-height":"400","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},t._l(t.columns,(function(s,a){return e("el-table-column",{key:a,attrs:{label:s.title,"min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[s.key?[e("div",[e("span",[t._v(t._s(a.row[s.key]))])])]:"number"===s.slot?[e("div",{class:a.row.pm?"plusColor":"reduceColor"},[t._v("\n                        "+t._s(a.row.pm?"+"+a.row.number:"-"+a.row.number)+"\n                      ")])]:t._e()]}}],null,!0)})})),1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.userFrom.page,limit:t.userFrom.limit},on:{"update:page":function(e){return t.$set(t.userFrom,"page",e)},"update:limit":function(e){return t.$set(t.userFrom,"limit",e)},pagination:t.changeType}}):t._e()],1)]],2)}))],2)],1)],1)],1)],1)}),[],!1,null,"530f37f5",null).exports),g=s("c42b"),s("8c03")),I=s("b562");d={name:"user_list",components:{expandRow:d,editFrom:b.a,sendFrom:_.a,userDetails:C,newsCategory:g.a,customerInfo:y.default,userLabel:o.a,userEdit:f},data:function(){return{dataLabel:[],selectDataLabel:[],userData:{},modals:!1,selectLabelShow:!1,labelShow:!1,customerShow:!1,promoterShow:!1,labelActive:{uid:0},formInline:{uid:0,spread_uid:0,image:""},pickerOptions:this.$timeOptions,collapse:!1,address:[],addresData:[],isShowSend:!0,modal13:!1,maxCols:4,scrollerHeight:"600",contentTop:"130",contentWidth:"98%",grid:{xl:6,lg:6,md:8,sm:12,xs:24},grid2:{xl:8,lg:8,md:8,sm:12,xs:24},loading:!1,total:0,userFrom:{label_id:"",user_type:"",status:"",is_promoter:"",nickname:"",page:1,limit:15,level:"",group_id:"",field_key:""},field_key:"",level:"",group_id:"",label_id:"",user_time_type:"",pay_count:"",userLists:[],FromData:null,selectionList:[],user_ids:"",selectedData:[],timeVal:[],groupList:[],levelList:[],labelFrom:{page:1,limit:""},labelLists:[],selectedIds:[],ids:[]}},computed:Object(r.a)({},Object(n.d)("media",["isMobile"])),created:function(){this.getList(),this.getCityList()},mounted:function(){this.userGroup(),this.levelLists()},methods:{getCityList:function(){var t=this;Object(I.b)().then((function(e){t.addresData=e.data}))},setUser:function(){var t=this,e=this.$refs.userEdit.formItem,s=[];this.$refs.userEdit.dataLabel.map((function(t){s.push(t.id)})),e.label_id=s,e.uid?Object(m.h)(e).then((function(e){t.modals=!1,t.$message.success(e.msg),t.getList()})).catch((function(e){t.$message.error(e)})):Object(m.F)(e).then((function(e){t.modals=!1,t.$message.success(e.msg),t.getList()})).catch((function(e){t.$message.error(e.msg)}))},onceGetList:function(){this.labelActive.uid=0,this.getList()},labelClose:function(){this.labelActive.uid=0,this.labelShow=!1,this.selectLabelShow=!1},putSend:function(t){var e=this;this.$refs[t].validate((function(s){if(s){if(!e.formInline.spread_uid)return e.$message.error("请上传用户");Object(v.c)(e.formInline).then((function(s){e.promoterShow=!1,e.$message.success(s.msg),e.getList(),e.$refs[t].resetFields()})).catch((function(t){e.$message.error(t.msg)}))}}))},isSel:function(t){return!t.is_del},groupLists:function(){var t=this;this.loading=!0,Object(m.O)(this.labelFrom).then(function(){var e=Object(l.a)(Object(i.a)().mark((function e(s){var a;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=s.data,t.labelLists=a.list;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))},userGroup:function(){var t=this;Object(m.L)({page:1,limit:""}).then((function(e){t.groupList=e.data.list}))},levelLists:function(){var t=this;Object(p.m)({page:1,limit:"",title:"",is_show:1}).then((function(e){t.levelList=e.data.list}))},setGroup:function(){var t,e=this;0===this.ids.length?this.$message.warning("请选择要设置分组的用户"):(t={uids:this.ids},this.$modalForm(Object(m.W)(t)).then((function(){return e.getList()})))},activeSelectData:function(t){this.selectLabelShow=!1,this.selectDataLabel=t||[]},removeTag:function(t,e){var s;this.selectDataLabel.splice(t,1),this.selectDataLabel.length?(s=[],this.selectDataLabel.forEach((function(t){s.push(t.id)})),this.userFrom.label_id=s.join(",")):this.userFrom.label_id="",e&&e.stopPropagation()},activeData:function(t){var e=this,s=[];t.length&&(t.map((function(t){s.push(t.id)})),Object(m.A)({uids:this.ids.join(","),label_id:s}).then((function(t){e.labelShow=!1,e.selectedIds=new Set,e.getList(),e.$message.success(t.msg)})))},changeMember:function(){this.userFrom.page=1,this.getList()},changeCountry:function(){"abroad"!==this.userFrom.country&&this.userFrom.country||(this.selectedData=[],this.userFrom.province="",this.userFrom.city="",this.address=[])},handleChange:function(t){this.selectedData=t.map((function(t){return t.label})),this.userFrom.province=this.selectedData[0],this.userFrom.city=this.selectedData[1]},onchangeTime:function(t){this.timeVal=t,this.userFrom.user_time=this.timeVal?this.timeVal.join("-"):""},userDetail:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(t.id)},changeMenu:function(t,e,s){var a=this,i=[],l=(i.push(t.uid),{uids:i});switch(e){case"1":this.edit(t);break;case"2":this.$modalForm(Object(p.c)(t.id)).then((function(){a.getList()}));break;case"3":this.giveLevelTime(t.uid);break;case"4":this.del(t,"清除 【 "+this.tenText(t.nickname)+" 】的会员等级",s,"user");break;case"5":this.$modalForm(Object(m.W)(l)).then((function(){return a.getList()}));break;case"6":this.openLabel(t);break;case"7":this.editS(t);break;default:this.del(t,"解除【 "+this.tenText(t.nickname)+" 】的上级推广人",s,"tuiguang")}},tenText:function(t){return 10<t.length?t.substr(0,10)+"...":t},openLabel:function(t){this.labelShow=!0,this.labelActive.uid=t.uid},openSelectLabel:function(){this.selectLabelShow=!0},editS:function(t){this.promoterShow=!0,this.formInline.uid=t.uid},customer:function(){this.customerShow=!0},imageObject:function(t){this.customerShow=!1,this.formInline.spread_uid=t.uid,this.formInline.image=t.image},cancel:function(t){this.promoterShow=!1,this.$refs[t].resetFields()},del:function(t,e,s,a){var i=this;e={title:e,num:s,url:("user"===a?"user/del_level/":"agent/stair/delete_spread/").concat(t.uid),method:"user"===a?"DELETE":"PUT",ids:"",width:600};this.$modalSure(e).then((function(t){i.$message.success(t.msg),i.getList()})).catch((function(t){i.$message.error(t.msg)}))},getList:function(){var t,e=this;this.selectDataLabel.length&&(t=[],this.selectDataLabel.forEach((function(e){t.push(e.id)})),this.userFrom.label_id=t.join(",")),this.userFrom.user_type=this.userFrom.user_type||"",this.userFrom.status=this.userFrom.status||"",this.userFrom.sex=this.userFrom.sex||"",this.userFrom.is_promoter=this.userFrom.is_promoter||"",this.userFrom.country=this.userFrom.country||"",this.userFrom.pay_count="all"===this.pay_count?"":this.pay_count,this.userFrom.user_time_type="all"===this.user_time_type?"":this.user_time_type,this.userFrom.field_key="all"===this.field_key?"":this.field_key,this.userFrom.level="all"===this.level?"":this.level,this.userFrom.group_id="all"===this.group_id?"":this.group_id,this.loading=!0,Object(p.f)(this.userFrom).then(function(){var t=Object(l.a)(Object(i.a)().mark((function t(s){var a;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a=s.data,e.userLists=a.list,e.total=a.count,e.loading=!1,e.$nextTick((function(){e.setChecked()}));case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},exportList:function(){var t=this;return Object(l.a)(Object(i.a)().mark((function e(){var s,a,l,r,o,n,c,d;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.selectDataLabel.length&&(s=[],t.selectDataLabel.forEach((function(t){s.push(t.id)})),t.userFrom.label_id=s.join(",")),t.ids.length&&(t.userFrom.ids=t.ids),t.userFrom.user_type=t.userFrom.user_type||"",t.userFrom.status=t.userFrom.status||"",t.userFrom.sex=t.userFrom.sex||"",t.userFrom.is_promoter=t.userFrom.is_promoter||"",t.userFrom.country=t.userFrom.country||"",t.userFrom.pay_count="all"===t.pay_count?"":t.pay_count,t.userFrom.user_time_type="all"===t.user_time_type?"":t.user_time_type,t.userFrom.field_key="all"===t.field_key?"":t.field_key,t.userFrom.level="all"===t.level?"":t.level,t.userFrom.group_id="all"===t.group_id?"":t.group_id,a=[],l=[],r=[],o="",(n=JSON.parse(JSON.stringify(t.userFrom))).page=1,c=0;case 16:if(c<n.page+1)return e.next=19,t.getExcelData(n);e.next=33;break;case 19:d=e.sent,o=o||d.filename,l.length||(l=d.fileKey),a.length||(a=d.header),d.export.length?(r=r.concat(d.export),n.page++,e.next=30):e.next=28;break;case 28:return t.$exportExcel(a,l,o,r),e.abrupt("return");case 30:c++,e.next=16;break;case 33:case"end":return e.stop()}}),e)})))()},getExcelData:function(t){return new Promise((function(e,s){Object(h.a)(t).then((function(t){e(t.data)}))}))},pageChange:function(){this.selectionList=[],this.getList()},userSearchs:function(){var t;this.userFrom.page=1,this.selectDataLabel.length?(t=[],this.selectDataLabel.forEach((function(e){t.push(e.id)})),this.userFrom.label_id=t.join(",")):this.userFrom.label_id="",this.getList()},reset:function(){this.userFrom={user_type:this.userFrom.user_type,status:"",sex:"",is_promoter:"",country:"",pay_count:"",user_time_type:"",user_time:"",nickname:"",field_key:"",level:"",group_id:"",label_id:"",page:1,limit:20},this.field_key="",this.level="",this.group_id="",this.dataLabel=[],this.selectDataLabel=[],this.user_time_type="",this.pay_count="",this.timeVal=[],this.selectedIds=new Set,this.getList()},getUserFrom:function(t){var e=this;Object(m.i)(t).then(function(){var t=Object(l.a)(Object(i.a)().mark((function t(s){return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.modals=!0,e.userData=s.data;case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$message.error(t.msg)}))},getOtherFrom:function(t){var e=this;this.$modalForm(Object(m.g)(t)).then((function(){return e.getList(1)}))},onchangeIsShow:function(t){var e=this;t={id:t.uid,status:t.status};Object(m.o)(t).then(function(){var t=Object(l.a)(Object(i.a)().mark((function t(s){return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$message.success(s.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$message.error(t.msg)}))},edit:function(t){this.getUserFrom(t.uid)},submitFail:function(){},sortChanged:function(t){this.userFrom[t.prop]=t.order,this.getList()},handleSelectAll:function(t){var e=this,s=[];t.map((function(t){s.push(t.uid)})),this.selectedIds=s,this.$nextTick((function(){e.setChecked()}))},handleSelectRow:function(t){var e=this,s=[];t.map((function(t){s.push(t.uid)})),this.selectedIds=s,this.$nextTick((function(){e.setChecked()}))},setChecked:function(){this.ids=Object(a.a)(this.selectedIds);var t,e=this.$refs.table.objData;for(t in e)this.selectedIds.has(e[t].uid)&&(e[t]._isChecked=!0)}}},s("7a55"),b=Object(u.a)(d,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never","body-style":{padding:0}}},[e("div",{staticClass:"padding-add"},[e("el-form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":"80px","label-position":"right",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[t.collapse?t._e():e("div",{staticClass:"acea-row search-form"},[e("div",[e("el-form-item",{attrs:{label:"客户标签：","label-for":"label_id"}},[e("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openSelectLabel}},[e("div",{staticStyle:{width:"222px"}},[t.selectDataLabel.length?e("div",t._l(t.selectDataLabel,(function(s,a){return e("el-tag",{key:a,staticClass:"mr10",attrs:{closable:""},on:{close:function(e){return t.removeTag(a)}}},[t._v("\n                        "+t._s(s.label_name)+"\n                      ")])})),1):e("span",{staticClass:"span"},[t._v("选择客户关联标签")])]),e("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})])]),e("el-form-item",{attrs:{label:"客户等级：","label-for":"level"}},[e("el-select",{staticClass:"form_content_width",attrs:{placeholder:"请选择客户等级",clearable:""},model:{value:t.level,callback:function(e){t.level=e},expression:"level"}},[e("el-option",{attrs:{value:"all",label:"全部"}},[t._v("全部")]),t._l(t.levelList,(function(t,s){return e("el-option",{key:s,attrs:{value:t.id,label:t.level_name}})}))],2)],1),e("el-form-item",{attrs:{label:"客户搜索：","label-for":"nickname"}},[e("el-input",{staticClass:"form_content_width",attrs:{placeholder:"请输入客户",clearable:""},model:{value:t.userFrom.nickname,callback:function(e){t.$set(t.userFrom,"nickname",e)},expression:"userFrom.nickname"}},[e("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.field_key,callback:function(e){t.field_key=e},expression:"field_key"}},[e("el-option",{attrs:{value:"user",label:"客户代表"}}),e("el-option",{attrs:{value:"store",label:"所属门店"}}),e("el-option",{attrs:{value:"tel",label:"手机号"}}),e("el-option",{attrs:{value:"name",label:"客户姓名"}})],1)],1)],1)],1),e("el-form-item",{staticClass:"search-form-sub"},[e("el-button",{attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("搜索")]),e("el-button",{staticClass:"ResetSearch",on:{click:function(e){return t.reset("userFrom")}}},[t._v("重置")])],1)],1)])],1)]),e("el-card",{staticClass:"ivu-mt mt16",attrs:{bordered:!1,shadow:"never","body-style":{padding:"0 20px 20px"}}},[e("el-tabs"),e("el-row",{attrs:{gutter:24,justify:"space-between"}},[e("el-col",{attrs:{span:24}},[e("el-button",{staticClass:"mr10",attrs:{type:"primary"},on:{click:t.exportList}},[t._v("导出")])],1),t.selectionList.length?e("el-col",{staticClass:"userAlert",attrs:{span:24}},[e("el-alert",{attrs:{"show-icon":""}},[e("template",{slot:"title"},[t._v("\n              已选择"),e("i",{staticClass:"userI"},[t._v(" "+t._s(t.selectionList.length)+" ")]),t._v("项\n            ")])],2)],1):t._e()],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:t.userLists,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"sort-change":t.sortChanged,select:t.handleSelectRow,"select-all":t.handleSelectAll}},[e("el-table-column",{attrs:{type:"selection",selectable:t.isSel,width:"55"}}),e("el-table-column",{attrs:{label:"客户ID","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("span",[t._v(t._s(s.row.id))])]}}])}),e("el-table-column",{attrs:{label:"姓名","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"acea-row"},[e("i",{directives:[{name:"show",rawName:"v-show",value:"男"===s.row.sex,expression:"scope.row.sex === '男'"}],staticClass:"el-icon-male",staticStyle:{color:"#2db7f5","font-size":"15px"}}),e("i",{directives:[{name:"show",rawName:"v-show",value:"女"===s.row.sex,expression:"scope.row.sex === '女'"}],staticClass:"el-icon-female",staticStyle:{color:"#ed4014","font-size":"15px"}}),e("div",{domProps:{textContent:t._s(s.row.name)}})])]}}])}),e("el-table-column",{attrs:{label:"会员卡号","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"acea-row"},[e("div",{domProps:{textContent:t._s(0==s.row.cardNumber?"未绑定":s.row.cardNumber)}})])]}}])}),e("el-table-column",{attrs:{label:"所属门店","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"acea-row"},[e("div",{domProps:{textContent:t._s(""==s.row.group_name?"未绑定":s.row.group_name)}})])]}}])}),e("el-table-column",{attrs:{label:"客户等级","min-width":"90"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.level||"-"))])]}}])}),e("el-table-column",{attrs:{label:"客户标签","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.tags||"-"))])]}}])}),e("el-table-column",{attrs:{label:"积分","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.points||"-"))])]}}])}),e("el-table-column",{attrs:{label:"所属员工","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.nickname))])]}}])}),e("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.tel))])]}}])}),e("el-table-column",{attrs:{label:"年龄","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.age))])]}}])}),e("el-table-column",{attrs:{label:"备注","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",[t._v(t._s(s.row.beizhu||"-"))])]}}])}),e("el-table-column",{attrs:{label:"操作",fixed:"right",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[1!=s.row.is_del?[e("a",{on:{click:function(e){return t.userDetail(s.row)}}},[t._v("详情")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-dropdown",{attrs:{size:"small",transfer:!0},on:{command:function(e){return t.changeMenu(s.row,e,s.$index)}}},[e("span",{staticClass:"el-dropdown-link"},[t._v("更多"),e("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{attrs:{command:"1"}},[t._v("编辑")]),e("el-dropdown-item",{attrs:{command:"2"}},[t._v("操作积分")])],1)],1)]:[e("a",{on:{click:function(e){return t.userDetail(s.row)}}},[t._v("详情")])]]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.userFrom.page,limit:t.userFrom.limit},on:{"update:page":function(e){return t.$set(t.userFrom,"page",e)},"update:limit":function(e){return t.$set(t.userFrom,"limit",e)},pagination:t.pageChange}}):t._e()],1)],1),e("edit-from",{ref:"edits",attrs:{FromData:t.FromData},on:{submitFail:t.submitFail}}),e("send-from",{ref:"sends",attrs:{userIds:t.ids.toString()}}),e("user-details",{ref:"userDetails"}),e("el-dialog",{attrs:{visible:t.promoterShow,title:"修改推广人",width:"540px","show-close":!0},on:{"update:visible":function(e){t.promoterShow=e}}},[e("el-form",{ref:"formInline",attrs:{model:t.formInline,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[t.formInline?e("el-form-item",{attrs:{label:"选择推广人：",prop:"image"}},[e("div",{staticClass:"picBox",on:{click:t.customer}},[t.formInline.image?e("div",{staticClass:"pictrue"},[e("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formInline.image,expression:"formInline.image"}]})]):e("div",{staticClass:"upLoad acea-row row-center-wrapper"},[e("i",{staticClass:"el-icon-user"})])])]):t._e()],1),e("div",{staticClass:"acea-row row-right mt20"},[e("el-button",{on:{click:function(e){return t.cancel("formInline")}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.putSend("formInline")}}},[t._v("提交")])],1)],1),e("el-dialog",{attrs:{visible:t.labelShow,title:"请选择客户标签",width:"540px","show-close":!0},on:{"update:visible":function(e){t.labelShow=e}}},[t.labelShow?e("userLabel",{attrs:{uid:t.labelActive.uid,only_get:!t.labelActive.uid},on:{close:t.labelClose,activeData:t.activeData,onceGetList:t.onceGetList}}):t._e()],1),e("el-drawer",{attrs:{visible:t.modals,wrapperClosable:!1,size:"720",title:"客户信息填写"},on:{"update:visible":function(e){t.modals=e}}},[t.modals?e("userEdit",{ref:"userEdit",attrs:{userData:t.userData}}):t._e(),e("div",{staticClass:"acea-row row-center"},[e("el-button",{on:{click:function(e){t.modals=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.setUser}},[t._v("提交")])],1)],1),e("el-dialog",{attrs:{visible:t.selectLabelShow,title:"请选择用户标签",width:"540px","show-close":!0,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selectLabelShow=e}}},[t.selectLabelShow?e("userLabel",{ref:"userLabel",attrs:{uid:0,only_get:!0},on:{activeData:t.activeSelectData,close:t.labelClose}}):t._e()],1)],1)}),[],!1,null,"e5e2a924",null);e.default=b.exports}}]);