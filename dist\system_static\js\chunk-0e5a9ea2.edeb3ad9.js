(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-0e5a9ea2"],{"1f20":function(e,t,a){},2803:function(e,t,a){},"6fbe":function(e,t,a){"use strict";a("b26f")},"8baa":function(e,t,a){"use strict";a("987a")},"8dab":function(e,t,a){},"987a":function(e,t,a){},a19c:function(e,t,a){"use strict";a("f707")},acd9:function(e,t,a){},b199:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var s=a("6b6c");function i(e){return Object(s.a)({url:"task/getList",method:"get",params:e})}},b26f:function(e,t,a){},bfe5:function(e,t,a){"use strict";a("acd9")},d8e8:function(e,t,a){"use strict";a("2803")},dc36:function(e,t,a){"use strict";a.r(t),a("d3b7"),a("25f0");var s=a("2909"),i=a("c7eb"),r=a("1da1"),l=a("5530"),o=(a("d81d"),a("14d9"),a("a15b"),a("6062"),a("3ca3"),a("ddb0"),a("159b"),a("b64b"),a("e9c4"),a("99af"),a("232f")),n=a("2f62"),c=a("61f7"),u={name:"table-expand",filters:{formatDate:function(e){if(0!==e)return e=new Date(1e3*e),Object(c.a)(e,"yyyy-MM-dd hh:mm")}},props:{row:Object}},d=(a("e8bf"),a("2877")),m=(u=Object(d.a)(u,(function(){var e=this,t=e._self._c;return t("div",[t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("首次访问：")]),t("span",{staticClass:"expand-value"},[e._v(" "+e._s(e._f("formatDate")(e.row.add_time)))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("近次访问：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e._f("formatDate")(e.row.last_time)))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("身份证号：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.card_id))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("真实姓名：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.real_name))])])],1),t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("标签：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.labels))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("生日：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.birthday))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("推荐人：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.spread_uid_nickname))])]),t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("地址：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.addres))])])],1),t("el-row",{staticClass:"expand-row"},[t("el-col",{attrs:{span:6}},[t("span",{staticClass:"expand-key"},[e._v("备注：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.mark))])])],1)],1)}),[],!1,null,"0ee8a294",null).exports,a("498a"),a("b0c0"),a("4de4"),a("a434"),a("c24f")),f={name:"userEdit",components:{userLabel:o.a},props:{userData:{type:Object,default:function(){}}},watch:{},data:function(){return{modals:!1,labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},mounted:function(){var e=this,t=(this.$set(this.infoData,"groupInfo",this.userData.groupInfo),this.$set(this.infoData,"levelInfo",this.userData.levelInfo),this.$set(this.infoData,"labelInfo",this.userData.labelInfo),Object.keys(this.formItem));this.userData.userInfo?(t.map((function(t){e.formItem[t]=e.userData.userInfo[t]})),this.formItem.birthday||(this.formItem.birthday=""),this.formItem.label_id.length&&(this.dataLabel=this.formItem.label_id)):this.reset()},methods:{addLabel:function(){this.$modalForm(Object(m.O)(0)).then((function(){}))},changeModal:function(e){e||(this.cancel(),this.reset())},openLabel:function(e){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},cancel:function(){},activeData:function(e){this.labelShow=!1,this.dataLabel=e},labelClose:function(){this.labelShow=!1},closeLabel:function(e){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id==e.id}))[0]);this.dataLabel.splice(t,1)},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}},p=(f=(a("dd85"),Object(d.a)(f,(function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"formItem",attrs:{rules:e.ruleValidate,model:e.formItem,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[e.formItem.uid?t("el-form-item",{attrs:{label:"用户ID："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{disabled:"",placeholder:"请输入编号"},model:{value:e.formItem.uid,callback:function(t){e.$set(e.formItem,"uid",t)},expression:"formItem.uid"}})],1):e._e(),t("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入真实姓名"},model:{value:e.formItem.real_name,callback:function(t){e.$set(e.formItem,"real_name","string"==typeof t?t.trim():t)},expression:"formItem.real_name"}})],1),t("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入手机号码"},model:{value:e.formItem.phone,callback:function(t){e.$set(e.formItem,"phone",t)},expression:"formItem.phone"}})],1),t("el-form-item",{attrs:{label:"生日："}},[t("el-date-picker",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formItem.birthday,callback:function(t){e.$set(e.formItem,"birthday",t)},expression:"formItem.birthday"}})],1),t("el-form-item",{attrs:{label:"身份证号："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入身份证号"},model:{value:e.formItem.card_id,callback:function(t){e.$set(e.formItem,"card_id","string"==typeof t?t.trim():t)},expression:"formItem.card_id"}})],1),t("el-form-item",{attrs:{label:"用户地址："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户地址"},model:{value:e.formItem.addres,callback:function(t){e.$set(e.formItem,"addres",t)},expression:"formItem.addres"}})],1),t("el-form-item",{attrs:{label:"用户备注："}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户备注"},model:{value:e.formItem.mark,callback:function(t){e.$set(e.formItem,"mark",t)},expression:"formItem.mark"}})],1),t("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.pwd,callback:function(t){e.$set(e.formItem,"pwd",t)},expression:"formItem.pwd"}})],1),t("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[t("el-input",{staticClass:"form-sty",staticStyle:{width:"80%"},attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.true_pwd,callback:function(t){e.$set(e.formItem,"true_pwd",t)},expression:"formItem.true_pwd"}})],1),t("el-form-item",{attrs:{label:"用户等级："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.level,callback:function(t){e.$set(e.formItem,"level",t)},expression:"formItem.level"}},e._l(e.infoData.levelInfo,(function(e,a){return t("el-option",{key:a,attrs:{value:e.id,label:e.name}})})),1)],1),t("el-form-item",{attrs:{label:"用户分组："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.group_id,callback:function(t){e.$set(e.formItem,"group_id",t)},expression:"formItem.group_id"}},e._l(e.infoData.groupInfo,(function(e,a){return t("el-option",{key:a,attrs:{value:e.id,label:e.group_name}})})),1)],1),t("el-form-item",{attrs:{label:"用户标签："}},[t("div",{staticStyle:{display:"flex"}},[t("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:e.openLabel}},[t("div",{staticStyle:{width:"90%"}},[e.dataLabel.length?t("div",e._l(e.dataLabel,(function(a,s){return t("el-tag",{key:s,staticClass:"mr10",attrs:{closable:""},on:{close:function(t){return e.closeLabel(a)}}},[e._v(e._s(a.label_name))])})),1):t("span",{staticClass:"span"},[e._v("选择用户关联标签")])]),t("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),t("span",{staticClass:"addfont",on:{click:e.addLabel}},[e._v("新增标签")])])]),t("el-form-item",{attrs:{label:"推广资格："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.spread_open,callback:function(t){e.$set(e.formItem,"spread_open",t)},expression:"formItem.spread_open"}},[t("el-radio",{attrs:{label:1}},[e._v("启用")]),t("el-radio",{attrs:{label:0}},[e._v("禁用")])],1),t("div",{staticClass:"tip"},[e._v("禁用用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1),t("el-form-item",{attrs:{label:"推广权限："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.is_promoter,callback:function(t){e.$set(e.formItem,"is_promoter",t)},expression:"formItem.is_promoter"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1),t("div",{staticClass:"tip"},[e._v("指定分销模式下，开启或关闭用户的推广权限")])],1),t("el-form-item",{attrs:{label:"用户状态："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.status,callback:function(t){e.$set(e.formItem,"status",t)},expression:"formItem.status"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1)],1)],1),t("el-dialog",{attrs:{visible:e.labelShow,scrollable:"",title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{only_get:!0,uid:e.formItem.uid},on:{close:e.labelClose,activeData:e.activeData}}):e._e()],1)],1)}),[],!1,null,"f2fd126a",null).exports),a("f6b0")),h=a("b199"),b=a("bbbc"),v=a("3f2a"),_=a("31b4"),g=a("a8e0"),w=a("5a0c"),I=a.n(w),y=(w={name:"userInfo",props:{psInfo:Object},filters:{timeFormat:function(e,t){return t=t?"YYYY-MM-DD":"YYYY-MM-DD HH:mm:ss",e?I()(1e3*e).format(t):"-"},gender:function(e){return 1==e?"男":2==e?"女":"未知"}},computed:{hasExtendInfo:function(){}}},w=(a("d8e8"),Object(d.a)(w,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-info"},[t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("基本信息")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("客户ID：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.id))])]),t("div",{staticClass:"item"},[t("div",[e._v("姓名：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("手机号码：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.tel||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("生日：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.birthday))])])])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("客户概况")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("客户等级：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.level_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("客户标签：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.tag_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("所属员工：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.parent_name||"-"))])]),t("div",{staticClass:"item"},[t("div",[e._v("添加时间：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.add_time))])])])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户备注")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("div",[e._v("备注：")]),t("div",{staticClass:"value"},[e._v(e._s(e.psInfo.beizhu||"-"))])])])])])}),[],!1,null,"574b9019",null).exports),a("a9e3"),{name:"userInfo",components:{userLabel:o.a},props:{userId:{type:Number,default:0}},filters:{timeFormat:function(e){return e?I()(1e3*e).format("YYYY-MM-DD HH:mm:ss"):"-"},gender:function(e){return 1==e?"男":2==e?"女":"未知"}},data:function(){return{labelShow:!1,formItem:{uid:0,real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1},groupInfo:[],labelInfo:[],levelInfo:[],infoData:{groupInfo:[],labelInfo:[],levelInfo:[]},ruleValidate:{real_name:[{required:!0,message:" ",trigger:"blur"}],phone:[{required:!0,message:" ",trigger:"blur"}],pwd:[{required:!0,message:" ",trigger:"blur"}],true_pwd:[{required:!0,message:" ",trigger:"blur"}]},dataLabel:[]}},computed:{hasExtendInfo:function(){}},created:function(){this.getUserFrom(this.userId)},methods:{setUser:function(){var e=this,t=this.formItem,a=[];this.dataLabel.map((function(e){a.push(e.id)})),t.label_id=a,t.uid?Object(m.j)(t).then((function(t){e.$message.success(t.msg),e.$emit("success")})).catch((function(t){e.$message.error(t.msg)})):Object(m.H)(t).then((function(t){e.$emit("success"),e.$message.success(t.msg)})).catch((function(t){e.$message.error("err.msg")}))},addLabel:function(){this.$modalForm(Object(m.O)(0)).then((function(){}))},openLabel:function(e){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.infoData.labelInfo)))},closeLabel:function(e){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id==e.id}))[0]);this.dataLabel.splice(t,1)},getUserFrom:function(e){var t=this;Object(m.k)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(a){var s;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.userData=a.data,t.$set(t.infoData,"groupInfo",t.userData.groupInfo),t.$set(t.infoData,"levelInfo",t.userData.levelInfo),t.$set(t.infoData,"labelInfo",t.userData.labelInfo),s=Object.keys(t.formItem),t.userData.userInfo?(s.map((function(e){t.formItem[e]=t.userData.userInfo[e]})),t.formItem.birthday||(t.formItem.birthday=""),t.formItem.label_id.length&&(t.dataLabel=t.formItem.label_id)):t.reset();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error("res.msg")}))},labelClose:function(){this.labelShow=!1},activeData:function(e){this.labelShow=!1,this.dataLabel=e},reset:function(){this.formItem={uid:"",real_name:"",phone:"",birthday:"",card_id:"",addres:"",mark:"",pwd:"",true_pwd:"",level:"",group_id:"",label_id:[],spread_open:0,is_promoter:0,status:1}}}}),C=(w=(a("bfe5"),{name:"userDetails",components:{userInfo:w,userEditForm:Object(d.a)(y,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-info"},[t("el-form",{ref:"formItem",attrs:{rules:e.ruleValidate,model:e.formItem,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("基本信息")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户ID："}},[t("el-input",{staticClass:"form-sty",attrs:{disabled:"",placeholder:"请输入编号"},model:{value:e.formItem.uid,callback:function(t){e.$set(e.formItem,"uid",t)},expression:"formItem.uid"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"真实姓名：",prop:"real_name"}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入真实姓名"},model:{value:e.formItem.real_name,callback:function(t){e.$set(e.formItem,"real_name","string"==typeof t?t.trim():t)},expression:"formItem.real_name"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入手机号码"},model:{value:e.formItem.phone,callback:function(t){e.$set(e.formItem,"phone",t)},expression:"formItem.phone"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"生日："}},[t("el-date-picker",{staticClass:"form-sty",attrs:{clearable:"",type:"date",placeholder:"请选择生日",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formItem.birthday,callback:function(t){e.$set(e.formItem,"birthday",t)},expression:"formItem.birthday"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"身份证号："}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入身份证号"},model:{value:e.formItem.card_id,callback:function(t){e.$set(e.formItem,"card_id","string"==typeof t?t.trim():t)},expression:"formItem.card_id"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户地址："}},[t("el-input",{staticClass:"form-sty",attrs:{placeholder:"请输入用户地址"},model:{value:e.formItem.addres,callback:function(t){e.$set(e.formItem,"addres",t)},expression:"formItem.addres"}})],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("密码")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"登录密码：",prop:"pwd"}},[t("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入登录密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.pwd,callback:function(t){e.$set(e.formItem,"pwd",t)},expression:"formItem.pwd"}})],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"确认密码：",prop:"true_pwd"}},[t("el-input",{staticClass:"form-sty",attrs:{type:"password",placeholder:"请输入确认密码（修改用户可不填写，不填写不修改原密码）"},model:{value:e.formItem.true_pwd,callback:function(t){e.$set(e.formItem,"true_pwd",t)},expression:"formItem.true_pwd"}})],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户概况")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户等级："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.level,callback:function(t){e.$set(e.formItem,"level",t)},expression:"formItem.level"}},e._l(e.infoData.levelInfo,(function(e,a){return t("el-option",{key:a,attrs:{value:e.id,label:e.name}})})),1)],1)],1),t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户分组："}},[t("el-select",{staticClass:"form-sty",attrs:{clearable:""},model:{value:e.formItem.group_id,callback:function(t){e.$set(e.formItem,"group_id",t)},expression:"formItem.group_id"}},e._l(e.infoData.groupInfo,(function(e,a){return t("el-option",{key:a,attrs:{value:e.id,label:e.group_name}})})),1)],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"用户标签："}},[t("div",{staticStyle:{display:"flex"}},[t("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:e.openLabel}},[t("div",{staticStyle:{width:"90%"}},[e.dataLabel.length?t("div",e._l(e.dataLabel,(function(a,s){return t("el-tag",{key:s,staticClass:"mr10",attrs:{closable:""},on:{close:function(t){return e.closeLabel(a)}}},[e._v(e._s(a.label_name))])})),1):t("span",{staticClass:"span"},[e._v("选择用户关联标签")])]),t("div",{staticClass:"ivu-icon ivu-icon-ios-arrow-down"})]),t("span",{staticClass:"addfont",on:{click:e.addLabel}},[e._v("新增标签")])])])],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"推广资格："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.spread_open,callback:function(t){e.$set(e.formItem,"spread_open",t)},expression:"formItem.spread_open"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("关闭")])],1),t("div",{staticClass:"tip"},[e._v("关闭用户的推广资格后，在任何分销模式下该用户都无分销权限")])],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"推广权限："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.is_promoter,callback:function(t){e.$set(e.formItem,"is_promoter",t)},expression:"formItem.is_promoter"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("关闭")]),t("div",{staticClass:"tip"},[e._v("指定分销模式下，开启或关闭用户的推广权限")])],1)],1)],1),t("div",{staticClass:"item lang"},[t("el-form-item",{attrs:{label:"用户状态："}},[t("el-radio-group",{staticClass:"form-sty",model:{value:e.formItem.status,callback:function(t){e.$set(e.formItem,"status",t)},expression:"formItem.status"}},[t("el-radio",{attrs:{label:1}},[e._v("开启")]),t("el-radio",{attrs:{label:0}},[e._v("锁定")])],1)],1)],1)])]),t("div",{staticClass:"section"},[t("div",{staticClass:"section-hd"},[e._v("用户备注")]),t("div",{staticClass:"section-bd"},[t("div",{staticClass:"item"},[t("el-form-item",{attrs:{label:"用户备注："}},[t("el-input",{staticClass:"form-sty",attrs:{type:"textarea",rows:5,placeholder:"请输入用户备注"},model:{value:e.formItem.mark,callback:function(t){e.$set(e.formItem,"mark",t)},expression:"formItem.mark"}})],1)],1)])])]),t("el-dialog",{attrs:{visible:e.labelShow,title:"请选择用户标签",modal:!1,"show-close":!0,width:"540px"},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{only_get:!0,uid:e.formItem.uid},on:{close:e.labelClose,activeData:e.activeData}}):e._e()],1)],1)}),[],!1,null,"4d45eecc",null).exports},data:function(){return{isEdit:!1,theme2:"light",list:[{val:"0",label:"任务要求"},{val:"1",label:"派发详情"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"order",page:1,limit:20},total:0,columns:[],userLists:[],psInfo:{},activeName:"0"}},created:function(){},methods:{edit:function(){this.activeName="user",this.isEdit=!this.isEdit},editSave:function(){this.$refs.editForm.setUser()},draChange:function(){this.isEdit=!1},getDetails:function(e){var t=this;this.activeName="0",this.userId=e,this.spinShow=!0,this.isEdit=!1,Object(p.f)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(a){var s;return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:200===a.status?(s=a.data,t.detailsData=s.headerList,t.psInfo=s.ps_info,t.spinShow=!1):(t.spinShow=!1,t.$message.error(a.msg));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.spinShow=!1,t.$message.error(e.msg)}))},changeTab:function(e){this.activeName=e.name,this.changeType()},changeType:function(){var e=this,t=(this.loading=!0,this.userFrom.type=this.activeName,this.isEdit=!1,{id:this.userId,type:this.userFrom.type});Object(p.q)(t).then(function(){var t=Object(r.a)(Object(i.a)().mark((function t(a){return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:200!==a.status?t.next=12:(t.t0=e.userFrom.type,t.next="0"===t.t0?4:"1"===t.t0?6:8);break;case 4:return e.columns=[{title:"ID",key:"id",minWidth:160},{title:"步骤",key:"content",minWidth:250},{title:"时间",key:"add_time",minWidth:90}],t.abrupt("break",8);case 6:return e.columns=[{title:"ID",key:"id",minWidth:160},{title:"内容",key:"content",minWidth:250},{title:"时间",key:"add_time",minWidth:90}],t.abrupt("break",8);case 8:e.$nextTick((function(t){var s=a.data;e.userLists=s.list,e.total=s.count})),e.loading=!1,t.next=14;break;case 12:e.loading=!1,e.$message.error(a.msg);case 14:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))}}}),y=(a("8baa"),a("6fbe"),Object(d.a)(w,(function(){var e=this,t=e._self._c;return t("div",{staticStyle:{width:"100%"}},[t("el-drawer",{attrs:{visible:e.modals,title:"任务详情",wrapperClosable:!1,size:1100},on:{"update:visible":function(t){e.modals=t},closed:e.draChange}},[t("el-row",{staticClass:"mt14",attrs:{justify:"space-between"}},[t("el-col",{attrs:{span:24}},[t("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.changeTab},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},e._l(e.list,(function(a,s){return t("el-tab-pane",{key:s,attrs:{name:a.val,label:a.label}},[[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",refInFor:!0,staticClass:"mt20",attrs:{data:e.userLists,"max-height":"400","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},e._l(e.columns,(function(a,s){return t("el-table-column",{key:s,attrs:{label:a.title,"min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[a.key?[t("div",[t("span",[e._v(e._s(s.row[a.key]))])])]:"number"===a.slot?[t("div",{class:s.row.pm?"plusColor":"reduceColor"},[e._v("\n                        "+e._s(s.row.pm?"+"+s.row.number:"-"+s.row.number)+"\n                      ")])]:e._e()]}}],null,!0)})})),1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.userFrom.page,limit:e.userFrom.limit},on:{"update:page":function(t){return e.$set(e.userFrom,"page",t)},"update:limit":function(t){return e.$set(e.userFrom,"limit",t)},pagination:e.changeType}}):e._e()],1)]],2)})),1)],1)],1)],1)],1)}),[],!1,null,"123481d1",null).exports),w=a("c42b"),a("8c03")),k=a("b562");u={name:"user_list",components:{expandRow:u,editFrom:_.a,sendFrom:g.a,userDetails:y,newsCategory:w.a,customerInfo:C.default,userLabel:o.a,userEdit:f},data:function(){return{dataLabel:[],selectDataLabel:[],userData:{},modals:!1,selectLabelShow:!1,labelShow:!1,customerShow:!1,promoterShow:!1,labelActive:{uid:0},formInline:{uid:0,spread_uid:0,image:""},pickerOptions:this.$timeOptions,collapse:!1,address:[],addresData:[],isShowSend:!0,modal13:!1,maxCols:4,scrollerHeight:"600",contentTop:"130",contentWidth:"98%",grid:{xl:6,lg:6,md:8,sm:12,xs:24},grid2:{xl:8,lg:8,md:8,sm:12,xs:24},loading:!1,total:0,userFrom:{page:1,limit:15},field_key:"",level:"",group_id:"",label_id:"",user_time_type:"",pay_count:"",userLists:[],FromData:null,selectionList:[],user_ids:"",selectedData:[],timeVal:[],groupList:[],levelList:[],labelFrom:{page:1,limit:""},labelLists:[],selectedIds:[],ids:[]}},computed:Object(l.a)({},Object(n.d)("media",["isMobile"])),created:function(){this.getList(),this.getCityList()},mounted:function(){this.userGroup(),this.levelLists()},methods:{getCityList:function(){var e=this;Object(k.b)().then((function(t){e.addresData=t.data}))},setUser:function(){var e=this,t=this.$refs.userEdit.formItem,a=[];this.$refs.userEdit.dataLabel.map((function(e){a.push(e.id)})),t.label_id=a,t.uid?Object(m.j)(t).then((function(t){e.modals=!1,e.$message.success(t.msg),e.getList()})).catch((function(t){e.$message.error(t)})):Object(m.H)(t).then((function(t){e.modals=!1,e.$message.success(t.msg),e.getList()})).catch((function(t){e.$message.error(t.msg)}))},onceGetList:function(){this.labelActive.uid=0,this.getList()},labelClose:function(){this.labelActive.uid=0,this.labelShow=!1,this.selectLabelShow=!1},putSend:function(e){var t=this;this.$refs[e].validate((function(a){if(a){if(!t.formInline.spread_uid)return t.$message.error("请上传用户");Object(b.c)(t.formInline).then((function(a){t.promoterShow=!1,t.$message.success(a.msg),t.getList(),t.$refs[e].resetFields()})).catch((function(e){t.$message.error(e.msg)}))}}))},isSel:function(e){return!e.is_del},groupLists:function(){var e=this;this.loading=!0,Object(m.Q)(this.labelFrom).then(function(){var t=Object(r.a)(Object(i.a)().mark((function t(a){var s;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:s=a.data,e.labelLists=s.list;case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},userGroup:function(){var e=this;Object(m.N)({page:1,limit:""}).then((function(t){e.groupList=t.data.list}))},levelLists:function(){var e=this;Object(p.r)({page:1,limit:"",title:"",is_show:1}).then((function(t){e.levelList=t.data.list}))},setGroup:function(){var e,t=this;0===this.ids.length?this.$message.warning("请选择要设置分组的用户"):(e={uids:this.ids},this.$modalForm(Object(m.Y)(e)).then((function(){return t.getList()})))},activeSelectData:function(e){this.selectLabelShow=!1,this.selectDataLabel=e||[]},activeData:function(e){var t=this,a=[];e.length&&(e.map((function(e){a.push(e.id)})),Object(m.C)({uids:this.ids.join(","),label_id:a}).then((function(e){t.labelShow=!1,t.selectedIds=new Set,t.getList(),t.$message.success(e.msg)})))},onchangeTime:function(e){this.timeVal=e,this.userFrom.user_time=this.timeVal?this.timeVal.join("-"):""},userDetail:function(e){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(e.uid)},changeMenu:function(e,t,a){var s=this,i=[],r=(i.push(e.uid),{uids:i});switch(t){case"1":this.edit(e);break;case"2":this.getOtherFrom(e.uid);break;case"3":this.giveLevelTime(e.uid);break;case"4":this.del(e,"清除 【 "+this.tenText(e.nickname)+" 】的会员等级",a,"user");break;case"5":this.$modalForm(Object(m.Y)(r)).then((function(){return s.getList()}));break;case"6":this.openLabel(e);break;case"7":this.editS(e);break;default:this.del(e,"解除【 "+this.tenText(e.nickname)+" 】的上级推广人",a,"tuiguang")}},tenText:function(e){return 10<e.length?e.substr(0,10)+"...":e},openLabel:function(e){this.labelShow=!0,this.labelActive.uid=e.uid},openSelectLabel:function(){this.selectLabelShow=!0},editS:function(e){this.promoterShow=!0,this.formInline.uid=e.uid},customer:function(){this.customerShow=!0},imageObject:function(e){this.customerShow=!1,this.formInline.spread_uid=e.uid,this.formInline.image=e.image},cancel:function(e){this.promoterShow=!1,this.$refs[e].resetFields()},del:function(e,t,a,s){var i=this;t={title:t,num:a,url:("user"===s?"user/del_level/":"agent/stair/delete_spread/").concat(e.uid),method:"user"===s?"DELETE":"PUT",ids:"",width:600};this.$modalSure(t).then((function(e){i.$message.success(e.msg),i.getList()})).catch((function(e){i.$message.error(e.msg)}))},getList:function(){var e=this;this.loading=!0,Object(h.a)(this.userFrom).then(function(){var t=Object(r.a)(Object(i.a)().mark((function t(a){var s;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:s=a.data,e.userLists=s.list,e.total=s.count,e.loading=!1,e.$nextTick((function(){e.setChecked()}));case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},exportList:function(){var e=this;return Object(r.a)(Object(i.a)().mark((function t(){var a,s,r,l,o,n,c,u;return Object(i.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.selectDataLabel.length&&(a=[],e.selectDataLabel.forEach((function(e){a.push(e.id)})),e.userFrom.label_id=a.join(",")),e.ids.length&&(e.userFrom.ids=e.ids),e.userFrom.user_type=e.userFrom.user_type||"",e.userFrom.status=e.userFrom.status||"",e.userFrom.sex=e.userFrom.sex||"",e.userFrom.is_promoter=e.userFrom.is_promoter||"",e.userFrom.country=e.userFrom.country||"",e.userFrom.pay_count="all"===e.pay_count?"":e.pay_count,e.userFrom.user_time_type="all"===e.user_time_type?"":e.user_time_type,e.userFrom.field_key="all"===e.field_key?"":e.field_key,e.userFrom.level="all"===e.level?"":e.level,e.userFrom.group_id="all"===e.group_id?"":e.group_id,s=[],r=[],l=[],o="",(n=JSON.parse(JSON.stringify(e.userFrom))).page=1,c=0;case 16:if(c<n.page+1)return t.next=19,e.getExcelData(n);t.next=33;break;case 19:u=t.sent,o=o||u.filename,r.length||(r=u.fileKey),s.length||(s=u.header),u.export.length?(l=l.concat(u.export),n.page++,t.next=30):t.next=28;break;case 28:return e.$exportExcel(s,r,o,l),t.abrupt("return");case 30:c++,t.next=16;break;case 33:case"end":return t.stop()}}),t)})))()},getExcelData:function(e){return new Promise((function(t,a){Object(v.c)(e).then((function(e){t(e.data)}))}))},pageChange:function(){this.selectionList=[],this.getList()},userSearchs:function(){this.userFrom.page=1,this.getList()},reset:function(e){this.userFrom={user_type:this.userFrom.user_type,status:"",sex:"",is_promoter:"",country:"",pay_count:"",user_time_type:"",user_time:"",nickname:"",field_key:"",level:"",group_id:"",label_id:"",page:1,limit:20},this.field_key="",this.level="",this.group_id="",this.dataLabel=[],this.selectDataLabel=[],this.user_time_type="",this.pay_count="",this.timeVal=[],this.selectedIds=new Set,this.getList()},getUserFrom:function(e){var t=this;Object(m.k)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(a){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.modals=!0,t.userData=a.data;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},getOtherFrom:function(e){var t=this;this.$modalForm(Object(m.i)(e)).then((function(){return t.getList(1)}))},onchangeIsShow:function(e){var t=this;e={id:e.uid,status:e.status};Object(m.q)(e).then(function(){var e=Object(r.a)(Object(i.a)().mark((function e(a){return Object(i.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$message.success(a.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},edit:function(e){this.getUserFrom(e.uid)},submitFail:function(){},sortChanged:function(e,t,a){this.userFrom[e.prop]=e.order,this.getList()},handleSelectAll:function(e){var t=this,a=[];e.map((function(e){a.push(e.uid)})),this.selectedIds=a,this.$nextTick((function(){t.setChecked()}))},handleSelectRow:function(e,t){var a=this,s=[];e.map((function(e){s.push(e.uid)})),this.selectedIds=s,this.$nextTick((function(){a.setChecked()}))},setChecked:function(){this.ids=Object(s.a)(this.selectedIds);var e,t=this.$refs.table.objData;for(e in t)this.selectedIds.has(t[e].uid)&&(t[e]._isChecked=!0)}}},a("a19c"),_=Object(d.a)(u,(function(){var e=this,t=e._self._c;return t("div",[t("el-card",{staticClass:"ivu-mt mt16",attrs:{bordered:!1,shadow:"never","body-style":{padding:"0 20px 20px"}}},[t("el-tabs"),t("el-row",{attrs:{gutter:24,justify:"space-between"}},[t("el-col",{attrs:{span:24}},[t("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-user-save"],expression:"['admin-user-save']"}],attrs:{type:"primary"},on:{click:function(t){return e.edit({uid:0})}}},[e._v("添加任务")]),t("el-button",{staticClass:"mr10",on:{click:e.exportList}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:e.userLists,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"sort-change":e.sortChanged,select:e.handleSelectRow,"select-all":e.handleSelectAll}},[t("el-table-column",{attrs:{label:"任务ID","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.id))])]}}])}),t("el-table-column",{attrs:{label:"任务标题","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"acea-row"},[t("i",{directives:[{name:"show",rawName:"v-show",value:"男"===a.row.sex,expression:"scope.row.sex === '男'"}],staticClass:"el-icon-male",staticStyle:{color:"#2db7f5","font-size":"15px"}}),t("i",{directives:[{name:"show",rawName:"v-show",value:"女"===a.row.sex,expression:"scope.row.sex === '女'"}],staticClass:"el-icon-female",staticStyle:{color:"#ed4014","font-size":"15px"}}),t("div",{domProps:{textContent:e._s(a.row.title)}})])]}}])}),t("el-table-column",{attrs:{label:"发布人","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.fb_name||"-"))])]}}])}),t("el-table-column",{attrs:{label:"发布时间","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.add_time||"-"))])]}}])}),t("el-table-column",{attrs:{label:"截止时间","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.end_time||"-"))])]}}])}),t("el-table-column",{attrs:{label:"操作",fixed:"right",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[1!=a.row.is_del?[t("a",{on:{click:function(t){return e.userDetail(a.row)}}},[e._v("详情")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-dropdown",{attrs:{size:"small",transfer:!0},on:{command:function(t){return e.changeMenu(a.row,t,a.$index)}}},[t("span",{staticClass:"el-dropdown-link"},[e._v("更多"),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"1"}},[e._v("编辑")]),t("el-dropdown-item",{attrs:{command:"5"}},[e._v("设置分组")])],1)],1)]:[t("a",{on:{click:function(t){return e.userDetail(a.row)}}},[e._v("详情")])]]}}])})],1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.userFrom.page,limit:e.userFrom.limit},on:{"update:page":function(t){return e.$set(e.userFrom,"page",t)},"update:limit":function(t){return e.$set(e.userFrom,"limit",t)},pagination:e.pageChange}}):e._e()],1)],1),t("edit-from",{ref:"edits",attrs:{FromData:e.FromData},on:{submitFail:e.submitFail}}),t("send-from",{ref:"sends",attrs:{userIds:e.ids.toString()}}),t("user-details",{ref:"userDetails"}),t("el-dialog",{attrs:{visible:e.promoterShow,title:"修改推广人",width:"540px","show-close":!0},on:{"update:visible":function(t){e.promoterShow=t}}},[t("el-form",{ref:"formInline",attrs:{model:e.formInline,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[e.formInline?t("el-form-item",{attrs:{label:"选择推广人：",prop:"image"}},[t("div",{staticClass:"picBox",on:{click:e.customer}},[e.formInline.image?t("div",{staticClass:"pictrue"},[t("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.formInline.image,expression:"formInline.image"}]})]):t("div",{staticClass:"upLoad acea-row row-center-wrapper"},[t("i",{staticClass:"el-icon-user"})])])]):e._e()],1),t("div",{staticClass:"acea-row row-right mt20"},[t("el-button",{on:{click:function(t){return e.cancel("formInline")}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.putSend("formInline")}}},[e._v("提交")])],1)],1),t("el-dialog",{attrs:{visible:e.customerShow,title:"请选择商城用户","show-close":!0,width:"1000px"},on:{"update:visible":function(t){e.customerShow=t}}},[e.customerShow?t("customerInfo",{on:{imageObject:e.imageObject}}):e._e()],1),t("el-dialog",{attrs:{visible:e.labelShow,title:"请选择用户标签",width:"540px","show-close":!0},on:{"update:visible":function(t){e.labelShow=t}}},[e.labelShow?t("userLabel",{attrs:{uid:e.labelActive.uid,only_get:!e.labelActive.uid},on:{close:e.labelClose,activeData:e.activeData,onceGetList:e.onceGetList}}):e._e()],1),t("el-drawer",{attrs:{visible:e.modals,wrapperClosable:!1,size:"720",title:"客户信息填写"},on:{"update:visible":function(t){e.modals=t}}},[e.modals?t("userEdit",{ref:"userEdit",attrs:{userData:e.userData}}):e._e(),t("div",{staticClass:"acea-row row-center"},[t("el-button",{on:{click:function(t){e.modals=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.setUser}},[e._v("提交")])],1)],1),t("el-dialog",{attrs:{visible:e.selectLabelShow,title:"请选择用户标签",width:"540px","show-close":!0,"close-on-click-modal":!1},on:{"update:visible":function(t){e.selectLabelShow=t}}},[e.selectLabelShow?t("userLabel",{ref:"userLabel",attrs:{uid:0,only_get:!0},on:{activeData:e.activeSelectData,close:e.labelClose}}):e._e()],1)],1)}),[],!1,null,"6c43916e",null);t.default=_.exports},dd85:function(e,t,a){"use strict";a("8dab")},e8bf:function(e,t,a){"use strict";a("1f20")},f707:function(e,t,a){}}]);