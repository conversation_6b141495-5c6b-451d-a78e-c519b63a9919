
<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mt">
      <div class="tabform">
        <el-form ref="formValidate" :model="formValidate" label-width="80px" inline @submit.native.prevent>
          <el-form-item label="类型名称：">
            <el-input
              clearable
              placeholder="请输入类型名称"
              v-model="formValidate.keyword"
              class="form_content_width"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select v-model="formValidate.status" placeholder="请选择状态" clearable class="form_content_width">
              <el-option label="全部" value=""></el-option>
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-row :gutter="24" justify="space-between">
        <el-col :span="24">
          <el-button type="primary" @click="add">添加积分项</el-button>
        </el-col>
      </el-row>

      <el-table
        :data="tableList"
        class="mt16"
        ref="table"
        highlight-current-row
        v-loading="loading"
        empty-text="暂无数据"
        no-filtered-data-text="暂无筛选结果"
      >
        <el-table-column label="编号" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型名称" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.type_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="积分值" min-width="100">
          <template slot-scope="scope">
            <span class="point-value">{{ scope.row.point }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
              @change="changeStatus(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="edit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" style="color: #f56c6c" @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="formValidate.page"
          :limit.sync="formValidate.limit"
          @pagination="getList"
        />
      </div>
    </el-card>

    <!-- 添加/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form
        ref="pointTypeForm"
        :model="pointTypeForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="类型名称" prop="type_name">
          <el-input
            v-model="pointTypeForm.type_name"
            placeholder="请输入类型名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="积分值" prop="point">
          <el-input-number
            v-model="pointTypeForm.point"
            :min="0"
            :max="99999"
            placeholder="请输入积分值"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="pointTypeForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPointTypeList,
  addPointType,
  editPointType,
  getPointTypeInfo,
  deletePointType,
  changePointTypeStatus
} from '@/api/customer';
import pagination from '@/components/Pagination';

export default {
  name: 'customer_point_type',
  components: {
    pagination
  },
  data() {
    return {
      loading: false,
      tableList: [],
      total: 0,
      formValidate: {
        page: 1,
        limit: 15,
        keyword: '',
        status: ''
      },
      dialogVisible: false,
      dialogTitle: '添加积分项',
      isEdit: false,
      editId: null,
      submitLoading: false,
      pointTypeForm: {
        type_name: '',
        point: 0,
        status: 1
      },
      rules: {
        type_name: [
          { required: true, message: '请输入类型名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        point: [
          { required: true, message: '请输入积分值', trigger: 'blur' },
          { type: 'number', min: 0, max: 99999, message: '积分值必须在 0 到 99999 之间', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取积分项列表
    getList() {
      this.loading = true;
      getPointTypeList(this.formValidate)
        .then(res => {
          this.tableList = res.data.list || [];
          this.total = res.data.count || 0;
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
          this.$message.error(err.msg || '获取积分项列表失败');
        });
    },
    // 搜索
    search() {
      this.formValidate.page = 1;
      this.getList();
    },
    // 重置
    reset() {
      this.formValidate.keyword = '';
      this.formValidate.status = '';
      this.formValidate.page = 1;
      this.getList();
    },
    // 添加积分项
    add() {
      this.dialogTitle = '添加积分项';
      this.isEdit = false;
      this.editId = null;
      this.dialogVisible = true;
    },
    // 编辑积分项
    edit(row) {
      this.dialogTitle = '编辑积分项';
      this.isEdit = true;
      this.editId = row.id;
      this.pointTypeForm = {
        type_name: row.type_name,
        point: row.point,
        status: row.status
      };
      this.dialogVisible = true;
    },
    // 删除积分项
    del(row) {
      this.$confirm('确定要删除这个积分项吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePointType(row.id)
          .then(res => {
            this.$message.success('删除成功');
            this.getList();
          })
          .catch(err => {
            this.$message.error(err.msg || '删除失败');
          });
      }).catch(() => {
        // 用户取消删除
      });
    },
    // 修改状态
    changeStatus(row) {
      changePointTypeStatus(row.id, row.status)
        .then(res => {
          this.$message.success('状态修改成功');
        })
        .catch(err => {
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1;
          this.$message.error(err.msg || '状态修改失败');
        });
    },
    // 提交表单
    submitForm() {
      this.$refs.pointTypeForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const apiCall = this.isEdit
            ? editPointType(this.editId, this.pointTypeForm)
            : addPointType(this.pointTypeForm);

          apiCall
            .then(res => {
              this.$message.success(this.isEdit ? '编辑成功' : '添加成功');
              this.dialogVisible = false;
              this.getList();
              this.submitLoading = false;
            })
            .catch(err => {
              this.$message.error(err.msg || (this.isEdit ? '编辑失败' : '添加失败'));
              this.submitLoading = false;
            });
        }
      });
    },
    // 重置表单
    resetForm() {
      this.pointTypeForm = {
        type_name: '',
        point: 0,
        status: 1
      };
      if (this.$refs.pointTypeForm) {
        this.$refs.pointTypeForm.resetFields();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.page {
  margin-top: 20px;
}
.mt16 {
  margin-top: 16px;
}
.tabform {
  margin-bottom: 10px;
}
.form_content_width {
  width: 220px;
}
.point-value {
  color: #67c23a;
  font-weight: 500;
}
.dialog-footer {
  text-align: right;
}
</style>