(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-2d21afa5"],{be8d:function(t,e,i){"use strict";i.r(e);var r=i("c7eb"),n=i("1da1"),a=i("5530"),o=(i("a434"),i("2f62")),s=i("c24f");a={name:"user_group",data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},loading:!1,groupFrom:{page:1,limit:15},groupLists:[],total:0}},computed:Object(a.a)(Object(a.a)({},Object(o.d)("media",["isMobile"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"left"}}),created:function(){this.getList()},methods:{add:function(){var t=this;this.$modalForm(Object(s.o)(0)).then((function(){return t.getList()}))},getList:function(){var t=this;this.loading=!0,Object(s.N)(this.groupFrom).then(function(){var e=Object(n.a)(Object(r.a)().mark((function e(i){var n;return Object(r.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=i.data,t.groupLists=n.list,t.total=n.count,t.loading=!1;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))},edit:function(t){var e=this;this.$modalForm(Object(s.o)(t)).then((function(){return e.getList()}))},del:function(t,e,i){var r=this;e={title:e,num:i,url:"user/user_group/del/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(e).then((function(t){r.$message.success(t.msg),r.groupLists.splice(i,1),r.getList()})).catch((function(t){r.$message.error(t.msg)}))}}},o=i("2877"),i=Object(o.a)(a,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never"}},[e("el-row",[e("el-col",t._b({},"el-col",t.grid,!1),[e("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-user-group"],expression:"['admin-user-group']"}],attrs:{type:"primary"},on:{click:t.add}},[t._v("添加分组")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt14",attrs:{data:t.groupLists,"highlight-current-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"ID",width:"80"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.id))])]}}])}),e("el-table-column",{attrs:{label:"分组","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.group_name))])]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("a",{on:{click:function(e){return t.edit(i.row.id)}}},[t._v("修改")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:function(e){return t.del(i.row,"删除分组",i.$index)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.groupFrom.page,limit:t.groupFrom.limit},on:{"update:page":function(e){return t.$set(t.groupFrom,"page",e)},"update:limit":function(e){return t.$set(t.groupFrom,"limit",e)},pagination:t.getList}}):t._e()],1)],1)],1)}),[],!1,null,"9a473dd4",null);e.default=i.exports}}]);