(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-6a6a54b2"],{"31b4":function(e,t,a){"use strict";a("a630"),a("3ca3");var s=a("5530"),l=(a("d3b7"),a("159b"),a("30ba")),i=(l=a.n(l),a("6b6c")),o=a("2f62");l={name:"edit",components:{formCreate:l.a.$form()},computed:Object(s.a)({},Object(o.d)("userLevel",["taskId","levelId"])),props:{FromData:{type:Object,default:null},update:{type:Boolean,default:!0}},watch:{FromData:function(){this.FromData.rules.forEach((function(e){e.title+="："}))}},data:function(){return{modals:!1,type:0,loading:!1,fapi:null,config:{form:{labelWidth:"100px"},resetBtn:!1,submitBtn:!1,global:{upload:{props:{onSuccess:function(e,t){200===e.status?t.url=e.data.src:this.$message.error(e.msg)}}}}}}},methods:{couponsType:function(){this.$parent.addType(this.type)},formSubmit:function(){this.fapi.submit()},onSubmit:function(e){var t=this;this.loading||(this.loading=!0,Object(i.a)({url:this.FromData.action,method:this.FromData.method,data:e}).then((function(e){t.update&&t.$parent.getList(),t.$message.success(e.msg),t.modals=!1,setTimeout((function(){t.$emit("submitFail"),t.loading=!1}),1e3)})).catch((function(e){t.loading=!1,t.$message.error(e.msg)})))},cancel:function(){this.type=0}}},a("fae3"),s=a("2877"),o=Object(s.a)(l,(function(){var e=this,t=e._self._c;return e.FromData?t("div",[t("el-dialog",{attrs:{visible:e.modals,title:e.FromData.title,"z-index":1,width:"720px"},on:{"update:visible":function(t){e.modals=t},closed:e.cancel}},[["/marketing/coupon/save.html"===e.FromData.action?t("div",{staticClass:"radio acea-row row-middle"},[t("div",{staticClass:"name ivu-form-item-content"},[e._v("优惠券类型")]),t("el-radio-group",{on:{input:e.couponsType},model:{value:e.type,callback:function(t){e.type=t},expression:"type"}},[t("el-radio",{attrs:{label:0}},[e._v("通用券")]),t("el-radio",{attrs:{label:1}},[e._v("品类券")]),t("el-radio",{attrs:{label:2}},[e._v("商品券")])],1)],1):e._e()],t("form-create",{ref:"fc",staticClass:"formBox",attrs:{option:e.config,rule:Array.from(this.FromData.rules),handleIcon:"false"},on:{submit:e.onSubmit},model:{value:e.fapi,callback:function(t){e.fapi=t},expression:"fapi"}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.modals=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.formSubmit}},[e._v("确 定")])],1)],2)],1):e._e()}),[],!1,null,"224af4d2",null);t.a=o.exports},4967:function(e,t,a){},9233:function(e,t,a){"use strict";a("fad6")},f49b:function(e,t,a){"use strict";a.r(t),a("b0c0");var s=a("c7eb"),l=a("1da1"),i=a("5530"),o=(a("a434"),a("2f62")),n=a("c24f"),r={name:"task",components:{editFrom:a("31b4").a},data:function(){return{grid:{xl:10,lg:10,md:12,sm:24,xs:24},modals:!1,levelFrom:{is_show:"",name:"",page:1,limit:20},total:0,levelLists:[],loading:!1,FromData:null,ids:0,modalTitleSs:"",titleType:"task"}},computed:Object(i.a)(Object(i.a)(Object(i.a)({},Object(o.d)("media",["isMobile"])),Object(o.d)("userLevel",["levelId"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:Object(i.a)(Object(i.a)({},Object(o.c)("userLevel",["getTaskId","getlevelId"])),{},{add:function(){this.ids="",this.getFrom()},getFrom:function(){var e=this,t={id:this.ids,level_id:this.levelId};this.$modalForm(Object(n.e)(t)).then((function(){return e.getList()}))},edit:function(e){this.ids=e.id,this.getFrom()},handleReset:function(){this.modals=!1},userSearchs:function(){this.getList()},getList:function(){var e=this;this.loading=!0,this.levelFrom.is_show=this.levelFrom.is_show||"",Object(n.G)(this.levelId,this.levelFrom).then(function(){var t=Object(l.a)(Object(s.a)().mark((function t(a){var l;return Object(s.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:l=a.data,e.levelLists=l.list,e.total=a.data.count,e.loading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},onchangeIsShow:function(e){var t=this;e={id:e.id,is_show:e.is_show};Object(n.E)(e).then(function(){var e=Object(l.a)(Object(s.a)().mark((function e(a){return Object(s.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$message.success(a.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},onchangeIsMust:function(e){var t=this;e={id:e.id,is_must:e.is_must};Object(n.D)(e).then(function(){var e=Object(l.a)(Object(s.a)().mark((function e(a){return Object(s.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$message.success(a.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},submitFail:function(){this.getList()},del:function(e,t,a){var s=this;t={title:t,num:a,url:"user/user_level/delete_task/".concat(e.id),method:"DELETE",ids:""};this.$modalSure(t).then((function(e){s.$message.success(e.msg),s.levelLists.splice(a,1)})).catch((function(e){s.$message.error(e.msg)}))}})},c=a("2877");r={name:"user_level",components:{taskList:Object(c.a)(r,(function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{visible:e.modals,title:"等级任务","close-on-click-modal":!1,width:"1000px"},on:{"update:visible":function(t){e.modals=t},closed:e.handleReset}},[t("el-form",{ref:"levelFrom",attrs:{model:e.levelFrom,"label-width":e.labelWidth,"label-position":e.labelPosition},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-row",{attrs:{gutter:24}},[t("el-col",e._b({},"el-col",e.grid,!1),[t("el-form-item",{attrs:{label:"等级状态："}},[t("el-select",{attrs:{placeholder:"是否显示",clearable:""},on:{change:e.userSearchs},model:{value:e.levelFrom.is_show,callback:function(t){e.$set(e.levelFrom,"is_show",t)},expression:"levelFrom.is_show"}},[t("el-option",{attrs:{value:"1",label:"显示"}}),t("el-option",{attrs:{value:"0",label:"不显示"}})],1)],1)],1),t("el-col",e._b({},"el-col",e.grid,!1),[t("el-form-item",{attrs:{label:"等级名称：",prop:"status2","label-for":"status2"}},[t("el-input",{staticStyle:{width:"100%"},attrs:{search:"","enter-button":"",placeholder:"请输入等级名称"},on:{"on-search":e.userSearchs},model:{value:e.levelFrom.name,callback:function(t){e.$set(e.levelFrom,"name",t)},expression:"levelFrom.name"}})],1)],1)],1)],1),t("el-divider",{attrs:{direction:"vertical",dashed:""}}),t("el-row",[t("el-col",e._b({staticClass:"mb15"},"el-col",e.grid,!1),[t("el-button",{attrs:{type:"primary"},on:{click:e.add}},[e._v("添加等级任务")])],1),t("el-col",{staticClass:"userAlert",attrs:{span:24}},[t("el-alert",{attrs:{"show-icon":"",closable:""}},[t("template",{slot:"title"},[t("div",[e._v("添加等级任务,任务类型中的{$num}会自动替换成限定数量+系统预设的单位生成任务名")])])],2)],1)],1),t("el-divider",{attrs:{direction:"vertical",dashed:""}}),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.levelLists,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},[t("el-table-column",{attrs:{label:"ID",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.id))])]}}])}),t("el-table-column",{attrs:{label:"等级名称","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.level_name))])]}}])}),t("el-table-column",{attrs:{label:"任务名称","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.name))])]}}])}),t("el-table-column",{attrs:{label:"是否显示","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-switch",{staticClass:"defineSwitch",attrs:{"active-value":1,"inactive-value":0,value:a.row.is_show,size:"large","active-text":"显示","inactive-text":"隐藏"},on:{change:function(t){return e.onchangeIsShow(a.row)}},model:{value:a.row.is_show,callback:function(t){e.$set(a.row,"is_show",t)},expression:"scope.row.is_show"}})]}}])}),t("el-table-column",{attrs:{label:"务必达成","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-switch",{staticClass:"defineSwitch",attrs:{"active-value":1,"inactive-value":0,value:a.row.is_must,"true-value":1,"false-value":0,size:"large","active-text":"全部","inactive-text":"其一"},on:{change:function(t){return e.onchangeIsMust(a.row)}},model:{value:a.row.is_must,callback:function(t){e.$set(a.row,"is_must",t)},expression:"scope.row.is_must"}})]}}])}),t("el-table-column",{attrs:{label:"任务说明","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.illustrate))])]}}])}),t("el-table-column",{attrs:{label:"操作",fixed:"right",width:"170"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("a",{on:{click:function(t){return e.edit(a.row)}}},[e._v("编辑 | ")]),t("a",{on:{click:function(t){return e.del(a.row,"删除等级任务",e.index)}}},[e._v(" 删除")])]}}])})],1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.levelFrom.page,limit:e.levelFrom.limit},on:{"update:page":function(t){return e.$set(e.levelFrom,"page",t)},"update:limit":function(t){return e.$set(e.levelFrom,"limit",t)},pagination:e.getList}}):e._e()],1),t("edit-from",{ref:"edits",attrs:{FromData:e.FromData,titleType:e.titleType},on:{submitFail:e.submitFail}})],1)}),[],!1,null,"29669d90",null).exports},data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},loading:!1,levelFrom:{is_show:"",title:"",page:1,limit:15},levelLists:[],total:0,FromData:null,imgName:"",visible:!1,levelId:0,modalTitleSs:"",titleType:"level",modelTask:!1,num:0}},created:function(){this.getList()},computed:Object(i.a)(Object(i.a)({},Object(o.d)("media",["isMobile"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:Object(i.a)(Object(i.a)({},Object(o.c)("userLevel",["getlevelId"])),{},{changeMenu:function(e,t,a){switch(this.levelId=e.id,t){case"1":this.getlevelId(this.levelId),this.$refs.tasks.modals=!0,this.$refs.tasks.getList();break;case"3":this.edit(e);break;default:this.del(e,"删除等级",a)}},del:function(e,t,a){var s=this;t={title:t,num:a,url:"user/user_level/delete/".concat(e.id),method:"put",ids:""};this.$modalSure(t).then((function(e){s.$message.success(e.msg),s.levelLists.splice(a,1),s.total--})).catch((function(e){s.$message.error(e.msg)}))},onchangeIsShow:function(e){var t=this;e={id:e.id,is_show:e.is_show};Object(n.C)(e).then(function(){var e=Object(l.a)(Object(s.a)().mark((function e(a){return Object(s.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$message.success(a.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},getList:function(){var e=this;this.loading=!0,this.levelFrom.is_show=this.levelFrom.is_show||"",Object(n.p)(this.levelFrom).then(function(){var t=Object(l.a)(Object(s.a)().mark((function t(a){var l;return Object(s.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:l=a.data,e.levelLists=l.list,e.total=a.data.count,e.loading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$message.error(t.msg)}))},add:function(){var e=this;this.levelId=0,this.$modalForm(Object(n.d)({id:this.levelId})).then((function(){return e.getList()}))},edit:function(e){var t=this;this.levelId=e.id,this.$modalForm(Object(n.d)({id:this.levelId})).then((function(){return t.getList()})),this.getlevelId(this.levelId)},userSearchs:function(){this.levelFrom.page=1,this.getList()},submitFail:function(){this.getList()}})},a("9233"),i=Object(c.a)(r,(function(){var e=this,t=e._self._c;return t("div",[t("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never","body-style":{padding:0}}},[t("div",{staticClass:"padding-add"},[t("el-form",{ref:"levelFrom",attrs:{model:e.levelFrom,"label-width":e.labelWidth,"label-position":e.labelPosition,inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"等级状态：","label-for":"status1"}},[t("el-select",{staticClass:"form_content_width",attrs:{placeholder:"请选择",clearable:"","element-id":"status1"},on:{change:e.userSearchs},model:{value:e.levelFrom.is_show,callback:function(t){e.$set(e.levelFrom,"is_show",t)},expression:"levelFrom.is_show"}},[t("el-option",{attrs:{value:"1",label:"显示"}}),t("el-option",{attrs:{value:"0",label:"不显示"}})],1)],1),t("el-form-item",{attrs:{label:"等级名称：","label-for":"title"}},[t("el-input",{staticClass:"form_content_width",attrs:{clearable:"",placeholder:"请输入等级名称"},model:{value:e.levelFrom.title,callback:function(t){e.$set(e.levelFrom,"title",t)},expression:"levelFrom.title"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.userSearchs}},[e._v("查询")])],1)],1)],1)]),t("el-card",{staticClass:"ivu-mt mt16",attrs:{bordered:!1,shadow:"never"}},[t("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-user-level_add"],expression:"['admin-user-level_add']"}],attrs:{type:"primary"},on:{click:e.add}},[e._v("添加用户等级")]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mt14",attrs:{data:e.levelLists,"highlight-current-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},[t("el-table-column",{attrs:{label:"ID",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.id))])]}}])}),t("el-table-column",{attrs:{label:"分类名称","min-width":"400"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.name))])]}}])}),t("el-table-column",{attrs:{label:"是否显示","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,value:a.row.is_show,size:"large"},on:{change:function(t){return e.onchangeIsShow(a.row)}},model:{value:a.row.is_show,callback:function(t){e.$set(a.row,"is_show",t)},expression:"scope.row.is_show"}})]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-dropdown",{attrs:{size:"small",transfer:!0},on:{command:function(t){return e.changeMenu(a.row,t,a.$index)}}},[t("span",{staticClass:"el-dropdown-link"},[e._v("更多"),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"3"}},[e._v("编辑等级")]),t("el-dropdown-item",{attrs:{command:"2"}},[e._v("删除等级")])],1)],1)]}}])})],1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.levelFrom.page,limit:e.levelFrom.limit},on:{"update:page":function(t){return e.$set(e.levelFrom,"page",t)},"update:limit":function(t){return e.$set(e.levelFrom,"limit",t)},pagination:e.getList}}):e._e()],1)],1),t("task-list",{ref:"tasks"})],1)}),[],!1,null,"daae4808",null);t.default=i.exports},fad6:function(e,t,a){},fae3:function(e,t,a){"use strict";a("4967")}}]);