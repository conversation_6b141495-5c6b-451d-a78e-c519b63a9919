(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-5c5831b5"],{"0c67":function(t,e,a){"use strict";a.r(e),a("b0c0");var l=a("5530"),s=(a("a434"),a("2f62")),n=a("c24f"),i=a("6b6c"),o=a("c7eb"),r=a("1da1"),c={name:"task",components:{editFrom:a("31b4").a},data:function(){return{grid:{xl:10,lg:10,md:12,sm:24,xs:24},modals:!1,levelFrom:{is_show:"",name:"",page:1,limit:20},total:0,levelLists:[],loading:!1,FromData:null,ids:0,modalTitleSs:"",titleType:"task"}},computed:Object(l.a)(Object(l.a)(Object(l.a)({},Object(s.d)("media",["isMobile"])),Object(s.d)("userLevel",["levelId"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:Object(l.a)(Object(l.a)({},Object(s.c)("userLevel",["getTaskId","getlevelId"])),{},{add:function(){this.ids="",this.getFrom()},getFrom:function(){var t=this,e={id:this.ids,level_id:this.levelId};this.$modalForm(Object(n.g)(e)).then((function(){return t.getList()}))},edit:function(t){this.ids=t.id,this.getFrom()},handleReset:function(){this.modals=!1},userSearchs:function(){this.getList()},getList:function(){var t=this;this.loading=!0,this.levelFrom.is_show=this.levelFrom.is_show||"",Object(n.I)(this.levelId,this.levelFrom).then(function(){var e=Object(r.a)(Object(o.a)().mark((function e(a){var l;return Object(o.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:l=a.data,t.levelLists=l.list,t.total=a.data.count,t.loading=!1;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))},onchangeIsShow:function(t){var e=this;t={id:t.id,is_show:t.is_show};Object(n.G)(t).then(function(){var t=Object(r.a)(Object(o.a)().mark((function t(a){return Object(o.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$message.success(a.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$message.error(t.msg)}))},onchangeIsMust:function(t){var e=this;t={id:t.id,is_must:t.is_must};Object(n.F)(t).then(function(){var t=Object(r.a)(Object(o.a)().mark((function t(a){return Object(o.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$message.success(a.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$message.error(t.msg)}))},submitFail:function(){this.getList()},del:function(t,e,a){var l=this;e={title:e,num:a,url:"user/user_level/delete_task/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(e).then((function(t){l.$message.success(t.msg),l.levelLists.splice(a,1)})).catch((function(t){l.$message.error(t.msg)}))}})},u=a("2877");c={name:"tonken_list",components:{taskList:Object(u.a)(c,(function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{visible:t.modals,title:"等级任务","close-on-click-modal":!1,width:"1000px"},on:{"update:visible":function(e){t.modals=e},closed:t.handleReset}},[e("el-form",{ref:"levelFrom",attrs:{model:t.levelFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-row",{attrs:{gutter:24}},[e("el-col",t._b({},"el-col",t.grid,!1),[e("el-form-item",{attrs:{label:"等级状态："}},[e("el-select",{attrs:{placeholder:"是否显示",clearable:""},on:{change:t.userSearchs},model:{value:t.levelFrom.is_show,callback:function(e){t.$set(t.levelFrom,"is_show",e)},expression:"levelFrom.is_show"}},[e("el-option",{attrs:{value:"1",label:"显示"}}),e("el-option",{attrs:{value:"0",label:"不显示"}})],1)],1)],1),e("el-col",t._b({},"el-col",t.grid,!1),[e("el-form-item",{attrs:{label:"等级名称：",prop:"status2","label-for":"status2"}},[e("el-input",{staticStyle:{width:"100%"},attrs:{search:"","enter-button":"",placeholder:"请输入等级名称"},on:{"on-search":t.userSearchs},model:{value:t.levelFrom.name,callback:function(e){t.$set(t.levelFrom,"name",e)},expression:"levelFrom.name"}})],1)],1)],1)],1),e("el-divider",{attrs:{direction:"vertical",dashed:""}}),e("el-row",[e("el-col",t._b({staticClass:"mb15"},"el-col",t.grid,!1),[e("el-button",{attrs:{type:"primary"},on:{click:t.add}},[t._v("添加TOKEN")])],1),e("el-col",{staticClass:"userAlert",attrs:{span:24}},[e("el-alert",{attrs:{"show-icon":"",closable:""}},[e("template",{slot:"title"},[e("div",[t._v("添加等级任务,任务类型中的{$num}会自动替换成限定数量+系统预设的单位生成任务名")])])],2)],1)],1),e("el-divider",{attrs:{direction:"vertical",dashed:""}}),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",attrs:{data:t.levelLists,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"ID",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.id))])]}}])}),e("el-table-column",{attrs:{label:"用户等级","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.level_name))])]}}])}),e("el-table-column",{attrs:{label:"","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.name))])]}}])}),e("el-table-column",{attrs:{label:"操作",fixed:"right",width:"170"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("a",{on:{click:function(e){return t.edit(a.row)}}},[t._v("编辑 | ")]),e("a",{on:{click:function(e){return t.del(a.row,"删除等级任务",t.index)}}},[t._v(" 删除")])]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.levelFrom.page,limit:t.levelFrom.limit},on:{"update:page":function(e){return t.$set(t.levelFrom,"page",e)},"update:limit":function(e){return t.$set(t.levelFrom,"limit",e)},pagination:t.getList}}):t._e()],1),e("edit-from",{ref:"edits",attrs:{FromData:t.FromData,titleType:t.titleType},on:{submitFail:t.submitFail}})],1)}),[],!1,null,"f7d886d4",null).exports},data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},loading:!1,levelFrom:{is_show:"",title:"",page:1,limit:15},levelLists:[],tokenLists:[],total:0,FromData:null,imgName:"",visible:!1,levelId:0,modalTitleSs:"",titleType:"level",modelTask:!1,num:0}},created:function(){this.getTokenList()},computed:Object(l.a)(Object(l.a)({},Object(s.d)("media",["isMobile"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:Object(l.a)(Object(l.a)({},Object(s.c)("userLevel",["getlevelId"])),{},{getTokenList:function(){var t=this;this.loading=!0,Object(i.a)({url:"token/token/list",method:"get",params:void 0}).then((function(e){var a=e.data;t.tokenLists=a.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.$message.error(e.msg)}))},changeMenu:function(t,e,a){switch(this.levelId=t.id,e){case"1":this.getlevelId(this.levelId),this.$refs.tasks.modals=!0,this.$refs.tasks.getList();break;case"3":this.edit(t);break;default:this.del(t,"删除Token",a)}},del:function(t,e,a){var l=this;e={title:e,num:a,url:"token/token/delete/".concat(t.id),method:"put",ids:""};this.$modalSure(e).then((function(t){l.$message.success(t.msg),l.levelLists.splice(a,1),l.total--})).catch((function(t){l.$message.error(t.msg)}))},add:function(){var t=this;this.levelId=0,this.$modalForm(Object(i.a)({url:"token/token/create",method:"get"})).then((function(){return t.getList()}))}})},a("7fa4"),l=Object(u.a)(c,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt mt16",attrs:{bordered:!1,shadow:"never"}},[e("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-token-token-add"],expression:"['admin-token-token-add']"}],attrs:{type:"primary"},on:{click:t.add}},[t._v("添加TOKEN")]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt14",attrs:{data:t.tokenLists,"highlight-current-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"ID",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.id))])]}}])}),e("el-table-column",{attrs:{label:"TOKEN","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.title)+"\n        ")]}}])}),e("el-table-column",{attrs:{label:"等级名称","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.userlevel.name)+"\n        ")]}}])}),e("el-table-column",{attrs:{label:"分组名称","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.usergroup.group_name))])]}}])}),e("el-table-column",{attrs:{label:"使用人","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.user?e("span",[t._v(t._s(a.row.user.nickname))]):e("span",[t._v("暂未使用")])]}}])}),e("el-table-column",{attrs:{label:"使用时间","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.update_time))])]}}])}),e("el-table-column",{attrs:{label:"添加时间","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.add_time))])]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-dropdown",{attrs:{size:"small",transfer:!0},on:{command:function(e){return t.changeMenu(a.row,e,a.$index)}}},[e("span",{staticClass:"el-dropdown-link"},[t._v("更多"),e("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{attrs:{command:"2"}},[t._v("删除")])],1)],1)]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.levelFrom.page,limit:t.levelFrom.limit},on:{"update:page":function(e){return t.$set(t.levelFrom,"page",e)},"update:limit":function(e){return t.$set(t.levelFrom,"limit",e)},pagination:t.getTokenList}}):t._e()],1)],1),e("task-list",{ref:"tasks"})],1)}),[],!1,null,"38e0446a",null);e.default=l.exports},"31b4":function(t,e,a){"use strict";a("a630"),a("3ca3");var l=a("5530"),s=(a("d3b7"),a("159b"),a("30ba")),n=(s=a.n(s),a("6b6c")),i=a("2f62");s={name:"edit",components:{formCreate:s.a.$form()},computed:Object(l.a)({},Object(i.d)("userLevel",["taskId","levelId"])),props:{FromData:{type:Object,default:null},update:{type:Boolean,default:!0}},watch:{FromData:function(){this.FromData.rules.forEach((function(t){t.title+="："}))}},data:function(){return{modals:!1,type:0,loading:!1,fapi:null,config:{form:{labelWidth:"100px"},resetBtn:!1,submitBtn:!1,global:{upload:{props:{onSuccess:function(t,e){200===t.status?e.url=t.data.src:this.$message.error(t.msg)}}}}}}},methods:{couponsType:function(){this.$parent.addType(this.type)},formSubmit:function(){this.fapi.submit()},onSubmit:function(t){var e=this;this.loading||(this.loading=!0,Object(n.a)({url:this.FromData.action,method:this.FromData.method,data:t}).then((function(t){e.update&&e.$parent.getList(),e.$message.success(t.msg),e.modals=!1,setTimeout((function(){e.$emit("submitFail"),e.loading=!1}),1e3)})).catch((function(t){e.loading=!1,e.$message.error(t.msg)})))},cancel:function(){this.type=0}}},a("fae3"),l=a("2877"),i=Object(l.a)(s,(function(){var t=this,e=t._self._c;return t.FromData?e("div",[e("el-dialog",{attrs:{visible:t.modals,title:t.FromData.title,"z-index":1,width:"720px"},on:{"update:visible":function(e){t.modals=e},closed:t.cancel}},[["/marketing/coupon/save.html"===t.FromData.action?e("div",{staticClass:"radio acea-row row-middle"},[e("div",{staticClass:"name ivu-form-item-content"},[t._v("优惠券类型")]),e("el-radio-group",{on:{input:t.couponsType},model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[e("el-radio",{attrs:{label:0}},[t._v("通用券")]),e("el-radio",{attrs:{label:1}},[t._v("品类券")]),e("el-radio",{attrs:{label:2}},[t._v("商品券")])],1)],1):t._e()],e("form-create",{ref:"fc",staticClass:"formBox",attrs:{option:t.config,rule:Array.from(this.FromData.rules),handleIcon:"false"},on:{submit:t.onSubmit},model:{value:t.fapi,callback:function(e){t.fapi=e},expression:"fapi"}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.modals=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.formSubmit}},[t._v("确 定")])],1)],2)],1):t._e()}),[],!1,null,"224af4d2",null);e.a=i.exports},"353f":function(t,e,a){},4967:function(t,e,a){},"7fa4":function(t,e,a){"use strict";a("353f")},fae3:function(t,e,a){"use strict";a("4967")}}]);