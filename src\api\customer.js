import request from '@/libs/request';
export function getCustomerList(params){
    return request({
        url:'customer/getList',
        method:'get',
        params:params
    })
}


export function getCustomerLabel(uid){
    return request({
        url:'customer/getCustomerLabel/'+uid,
        method:'get',
    })
}

export function getCustomerLabelList(params){
    return request({
        url:'customer/getCustomerLabelList',
        method:'get',
        params:params
    })
}
export function getCustomerLabelForm(id){
    return request({
        url:'customer/label/add',
        method:'get',
        params: {id:id}
    })
}
export function levelListApi(){
    return request({
        url:'customer/level/list',
        method:'get',
    })
}

export function createApi(id){
    return request({
        url:'customer/level/create/'+id,
        method:'get',
    })
}

export function detailsApi(id) {
    return request({
        url: `customer/info/${id}`,
        method: 'get',
    });
}
export function infoApi(params){
    return request({
        url:'customer/one_info',
        method:'get',
        params:params
    })
}

/**
 * @description 客户积分记录列表
 * @param {Object} params 查询参数
 */
export function getPointRecordList(params) {
    return request({
        url: 'customer/point_record',
        method: 'get',
        params
    });
}

/**
 * @description 根据会员卡号查询客户积分信息
 * @param {String} cardNumber 会员卡号
 */
export function getCustomerPointByCard(cardNumber) {
    return request({
        url: 'customer/point/card_info',
        method: 'get',
        params: { card_number: cardNumber }
    });
}
export function editPoint(id){
    return request({
        url:'/customer/editPoint/'+id,
        method:'get'
    })
}

/**
 * @description 移交客户
 * @param {Object} data 移交数据 {customer_id: 客户ID, staff_phone: 员工手机号}
 */
export function transferCustomer(data) {
    return request({
        url: 'customer/transfer',
        method: 'post',
        data
    });
}

/**
 * @description 删除客户
 * @param {Number} id 客户ID
 */
export function deleteCustomer(id) {
    return request({
        url: `customer/delete/${id}`,
        method: 'DELETE'
    });
}

/**
 * @description 获取积分项列表
 * @param {Object} params 查询参数
 */
export function getPointTypeList(params) {
    return request({
        url: 'customer/point_type/list',
        method: 'get',
        params
    });
}

/**
 * @description 添加积分项
 * @param {Object} data 积分项数据
 */
export function addPointType(data) {
    return request({
        url: 'customer/point_type/add',
        method: 'post',
        data
    });
}

/**
 * @description 编辑积分项
 * @param {Number} id 积分项ID
 * @param {Object} data 积分项数据
 */
export function editPointType(id, data) {
    return request({
        url: `customer/point_type/edit/${id}`,
        method: 'post',
        data
    });
}

/**
 * @description 获取积分项详情
 * @param {Number} id 积分项ID
 */
export function getPointTypeInfo(id) {
    return request({
        url: `customer/point_type/info/${id}`,
        method: 'get'
    });
}

/**
 * @description 删除积分项
 * @param {Number} id 积分项ID
 */
export function deletePointType(id) {
    return request({
        url: `customer/point_type/delete/${id}`,
        method: 'DELETE'
    });
}

/**
 * @description 修改积分项状态
 * @param {Number} id 积分项ID
 * @param {Number} status 状态 1启用 0禁用
 */
export function changePointTypeStatus(id, status) {
    return request({
        url: `customer/point_type/status/${id}/${status}`,
        method: 'put'
    });
}

export function getSignCustomerList(params){
    return request({
        url:'customer/signCustomerList',
        method:'get',
        params:params
    })
}
export function getSignUserList(params){
    return request({
        url:'customer/getSignUserList',
        method:'get',
        params:params
    })
}
export function signCustomerSaveApi(data){
    return request({
        url:'customer/signCustomerSave',
        method:'post',
        data:data
    })
}
export function signCustomerUpdateApi(data){
    return request({
        url:'customer/signCustomerUpdate',
        method:'post',
        data:data
    })
}
export function getSingCustomerApi(params){
    return request({
        url:'customer/getSignCustomer',
        method:'get',
        params:params

    })
}