(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-520c3b7e"],{"0f2f":function(t,e,n){"use strict";n.r(e),n("ac1f"),n("841c"),n("b0c0"),n("a15b");var r=n("f6b0"),a={name:"customer_point_record",components:{pagination:n("333d").a},data:function(){return{loading:!1,tableList:[],total:0,timeVal:[],formValidate:{page:1,limit:15,keyword:"",time:""}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(r.l)(this.formValidate).then((function(e){t.tableList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.msg||"获取积分记录列表失败")}))},search:function(){this.formValidate.page=1,this.getList()},reset:function(){this.formValidate.keyword="",this.formValidate.time="",this.timeVal=[],this.formValidate.page=1,this.getList()},onchangeTime:function(t){this.timeVal=t||[],this.formValidate.time=this.timeVal[0]&&this.timeVal?this.timeVal.join("-"):"",this.formValidate.page=1}}};n("2bfe"),n=n("2877"),n=Object(n.a)(a,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never"}},[e("div",{staticClass:"tabform"},[e("el-form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":"80px",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"搜索日期："}},[e("el-date-picker",{staticStyle:{width:"250px"},attrs:{editable:!1,"value-format":"yyyy/MM/dd",type:"daterange",placement:"bottom-end","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),e("el-form-item",{attrs:{label:"客户名称："}},[e("el-input",{staticClass:"form_content_width",attrs:{clearable:"",placeholder:"请输入客户名称"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")]),e("el-button",{on:{click:t.reset}},[t._v("重置")])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:t.tableList,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"ID","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.id))])]}}])}),e("el-table-column",{attrs:{label:"客户名称","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.name))])]}}])}),e("el-table-column",{attrs:{label:"积分","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",{class:0<n.row.point?"text-danger":"text-success"},[t._v(t._s(n.row.point))])]}}])}),e("el-table-column",{attrs:{label:"缘由","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.des))])]}}])}),e("el-table-column",{attrs:{label:"时间","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.add_time))])]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.formValidate.page,limit:t.formValidate.limit},on:{"update:page":function(e){return t.$set(t.formValidate,"page",e)},"update:limit":function(e){return t.$set(t.formValidate,"limit",e)},pagination:t.getList}}):t._e()],1)],1)],1)}),[],!1,null,"15fd9402",null);e.default=n.exports},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},"2bfe":function(t,e,n){"use strict";n("e597")},"841c":function(t,e,n){"use strict";var r=n("c65b"),a=n("d784"),o=n("825a"),i=n("7234"),u=n("1d80"),c=n("129f"),s=n("577e"),l=n("dc4a"),d=n("14c3");a("search",(function(t,e,n){return[function(e){var n=u(this),a=i(e)?void 0:l(e,t);return a?r(a,e,n):new RegExp(e)[t](s(n))},function(t){var r=o(this),a=(t=s(t),n(e,r,t));return a.done?a.value:(a=r.lastIndex,c(a,0)||(r.lastIndex=0),t=d(r,t),c(r.lastIndex,a)||(r.lastIndex=a),null===t?-1:t.index)}]}))},e597:function(t,e,n){},f6b0:function(t,e,n){"use strict";n.d(e,"j",(function(){return a})),n.d(e,"i",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"r",(function(){return u})),n.d(e,"c",(function(){return c})),n.d(e,"f",(function(){return s})),n.d(e,"q",(function(){return l})),n.d(e,"l",(function(){return d})),n.d(e,"k",(function(){return m})),n.d(e,"v",(function(){return f})),n.d(e,"d",(function(){return p})),n.d(e,"m",(function(){return h})),n.d(e,"a",(function(){return b})),n.d(e,"g",(function(){return g})),n.d(e,"e",(function(){return _})),n.d(e,"b",(function(){return v})),n.d(e,"n",(function(){return w})),n.d(e,"o",(function(){return j})),n.d(e,"t",(function(){return O})),n.d(e,"u",(function(){return V})),n.d(e,"p",(function(){return y})),n.d(e,"s",(function(){return k})),n("99af");var r=n("6b6c");function a(t){return Object(r.a)({url:"customer/getList",method:"get",params:t})}function o(t){return Object(r.a)({url:"customer/getCustomerLabelList",method:"get",params:t})}function i(t){return Object(r.a)({url:"customer/label/add",method:"get",params:{id:t}})}function u(){return Object(r.a)({url:"customer/level/list",method:"get"})}function c(t){return Object(r.a)({url:"customer/level/create/"+t,method:"get"})}function s(t){return Object(r.a)({url:"customer/info/".concat(t),method:"get"})}function l(t){return Object(r.a)({url:"customer/one_info",method:"get",params:t})}function d(t){return Object(r.a)({url:"customer/point_record",method:"get",params:t})}function m(t){return Object(r.a)({url:"customer/point/card_info",method:"get",params:{card_number:t}})}function f(t){return Object(r.a)({url:"customer/transfer",method:"post",data:t})}function p(t){return Object(r.a)({url:"customer/delete/".concat(t),method:"DELETE"})}function h(t){return Object(r.a)({url:"customer/point_type/list",method:"get",params:t})}function b(t){return Object(r.a)({url:"customer/point_type/add",method:"post",data:t})}function g(t,e){return Object(r.a)({url:"customer/point_type/edit/".concat(t),method:"post",data:e})}function _(t){return Object(r.a)({url:"customer/point_type/delete/".concat(t),method:"DELETE"})}function v(t,e){return Object(r.a)({url:"customer/point_type/status/".concat(t,"/").concat(e),method:"put"})}function w(t){return Object(r.a)({url:"customer/signCustomerList",method:"get",params:t})}function j(t){return Object(r.a)({url:"customer/getSignUserList",method:"get",params:t})}function O(t){return Object(r.a)({url:"customer/signCustomerSave",method:"post",data:t})}function V(t){return Object(r.a)({url:"customer/signCustomerUpdate",method:"post",data:t})}function y(t){return Object(r.a)({url:"customer/getSignCustomer",method:"get",params:t})}function k(t){return Object(r.a)({url:"customer/operateCustomerPoints",method:"post",data:t})}}}]);