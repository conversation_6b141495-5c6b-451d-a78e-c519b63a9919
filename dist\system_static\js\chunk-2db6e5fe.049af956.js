(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-2db6e5fe"],{"1dc4":function(t,e,o){"use strict";o("e8be")},"265f":function(t,e,o){"use strict";o.r(e);var r=o("5530"),i=o("c24f"),n=o("2f62"),a=(o("c7cd"),o("53ca")),s=(o("ac1f"),o("00b4"),o("d3b7"),o("3ca3"),o("ddb0"),o("2b3d"),o("9861"),o("88a7"),o("271a"),o("5494"),o("466d"),o("81b2"),o("0eb6"),o("b7ef"),o("8bd4"),o("ace4"),o("5cc6"),o("907a"),o("9a8c"),o("a975"),o("735e"),o("c1ac"),o("d139"),o("3a7b"),o("986a"),o("1d02"),o("d5d6"),o("82f8"),o("e91f"),o("60bd"),o("5f96"),o("3280"),o("3fcc"),o("ca91"),o("25a1"),o("cd26"),o("3c5d"),o("2954"),o("649e"),o("219c"),o("170b"),o("b39a9"),o("6ce5"),o("2834"),o("72f7"),o("4ea1"),o("99af"),o("7e79")),c=o("90e7"),h=(s={name:"CropperImage",components:{VueCropper:s.VueCropper},data:function(){return{name:"",resImg:"",previews:{},option:{img:"",outputSize:1,outputType:"png",info:!0,canScale:!0,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixed:!0,fixedNumber:[1,1],full:!1,fixedBox:!1,canMove:!0,canMoveBox:!0,original:!1,centerBox:!0,height:!1,infoTrue:!1,maxImgSize:3e3,enlarge:1,mode:"300px 300px"}}},methods:{imgLoad:function(t){},changeScale:function(t){this.$refs.cropper.changeScale(t=t||1)},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},realTime:function(t){var e=this,o=this;o.previews=t,this.$refs.cropper.getCropBlob((function(t){e.blobToDataURI(t,(function(t){o.previewImg=t}))}))},blobToDataURI:function(t,e){var o=new FileReader;o.readAsDataURL(t),o.onload=function(t){e(t.target.result)}},selectImg:function(t){var e=this,o=t.target.files[0];if(!/\.(jpg|jpeg|png|JPG|PNG)$/.test(t.target.value))return this.$message({message:"图片类型要求：jpeg、jpg、png",type:"error"}),!1;t=new FileReader,t.onload=function(t){t="object"===Object(a.a)(t.target.result)?window.URL.createObjectURL(new Blob([t.target.result])):t.target.result,e.option.img=t},t.readAsDataURL(o)},base64ImgtoFile:function(t){for(var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"file",o=(t=t.split(","),t[0].match(/:(.*?);/)[1]),r=o.split("/")[1],i=atob(t[1]),n=i.length,a=new Uint8Array(n);n--;)a[n]=i.charCodeAt(n);return new File([a],"".concat(e,".").concat(r),{type:o})},uploadFile:function(t){var e=this,o=new FormData;o.append("file",t),Object(c.o)(o).then((function(t){200==t.status?e.$emit("uploadImgSuccess",t.data):e.$message({message:"上传失败",type:"error",duration:1e3})}))},uploadImg:function(){var t=this;this.$refs.cropper.getCropData((function(e){t.resImg=t.base64ImgtoFile(e),t.uploadFile(t.resImg)}))}}},o("3a12"),o("2877"));s={name:"setting_user",components:{CropperImg:Object(h.a)(s,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"cropper-content"},[e("div",{staticClass:"cropper-box"},[e("div",{staticClass:"cropper"},[e("vue-cropper",{ref:"cropper",attrs:{img:t.option.img,outputSize:t.option.outputSize,outputType:t.option.outputType,info:t.option.info,canScale:t.option.canScale,autoCrop:t.option.autoCrop,autoCropWidth:t.option.autoCropWidth,autoCropHeight:t.option.autoCropHeight,fixed:t.option.fixed,fixedNumber:t.option.fixedNumber,full:t.option.full,fixedBox:t.option.fixedBox,canMove:t.option.canMove,canMoveBox:t.option.canMoveBox,original:t.option.original,centerBox:t.option.centerBox,height:t.option.height,infoTrue:t.option.infoTrue,maxImgSize:t.option.maxImgSize,enlarge:t.option.enlarge,mode:t.option.mode},on:{realTime:t.realTime,imgLoad:t.imgLoad}})],1),e("div",{staticClass:"footer-btn"},[e("div",{staticClass:"scope-btn"},[e("input",{staticStyle:{position:"absolute",clip:"rect(0 0 0 0)"},attrs:{type:"file",id:"uploads",accept:"image/png, image/jpeg, image/gif, image/jpg"},on:{change:function(e){return t.selectImg(e)}}}),e("el-button",{attrs:{size:"mini",type:"danger",plain:"",icon:"el-icon-zoom-in"},on:{click:function(e){return t.changeScale(1)}}},[t._v("放大")]),e("el-button",{attrs:{size:"mini",type:"danger",plain:"",icon:"el-icon-zoom-out"},on:{click:function(e){return t.changeScale(-1)}}},[t._v("缩小")]),e("el-button",{attrs:{size:"mini",type:"danger",plain:""},on:{click:t.rotateLeft}},[t._v("↺ 左旋转")]),e("el-button",{attrs:{size:"mini",type:"danger",plain:""},on:{click:t.rotateRight}},[t._v("↻ 右旋转")])],1)])]),e("div",{staticClass:"show-preview"},[e("div",{staticClass:"preview"},[e("img",{style:t.previews.img,attrs:{src:t.previews.url}})]),e("div",{staticClass:"upload-btn"},[e("label",{staticClass:"btn",attrs:{for:"uploads"}},[t._v("选择图片")]),e("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(e){return t.uploadImg()}}},[t._v("确认上传")])],1)])])}),[],!1,null,"235c65cc",null).exports},computed:Object(r.a)(Object(r.a)(Object(r.a)({},Object(n.d)("media",["isMobile"])),Object(n.d)("userLevel",["categoryId"])),{},{labelWidth:function(){return this.isMobile?void 0:"80px"},labelPosition:function(){return this.isMobile?"top":"right"}}),data:function(){return{account:"",avatarMoadl:!1,formValidate:{avatar:"",real_name:"",pwd:"",new_pwd:"",conf_pwd:""},ruleValidate:{real_name:[{required:!0,message:"您的姓名不能为空",trigger:"blur"}]}}},mounted:function(){this.account=this.$store.state.userInfo.userInfo.account,this.formValidate.head_pic=this.$store.state.userInfo.userInfo.head_pic,this.formValidate.real_name=this.$store.state.userInfo.userInfo.real_name},methods:{uploadImgSuccess:function(t){this.avatarMoadl=!1,this.formValidate.head_pic=t.src},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){t?Object(i.J)(e.formValidate).then((function(t){e.$store.commit("userInfo/userRealName",e.formValidate.real_name),e.$store.commit("userInfo/userRealHeadPic",e.formValidate.head_pic),e.$message.success(t.msg)})).catch((function(t){e.$message.error(t.msg)})):e.formValidate.new_pwd!==e.formValidate.conf_pwd&&e.$message.error("您输入的新密码与旧密码不一致")}))}}},o("1dc4"),r=Object(h.a)(s,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never"}},[e("el-form",{ref:"formValidate",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":"160px","label-position":"right"}},[e("el-form-item",{attrs:{label:"头像："}},[e("div",{staticClass:"avatar",on:{click:function(e){t.avatarMoadl=!0}}},[t.formValidate.head_pic?e("img",{attrs:{src:t.formValidate.head_pic,alt:""}}):e("img",{attrs:{src:o("cdfe"),alt:""}})])]),e("el-form-item",{attrs:{label:"账号：",prop:""}},[e("el-input",{staticClass:"input",attrs:{type:"text",disabled:!0},model:{value:t.account,callback:function(e){t.account=e},expression:"account"}})],1),e("el-form-item",{attrs:{label:"姓名：",prop:"real_name"}},[e("el-input",{staticClass:"input",attrs:{type:"text"},model:{value:t.formValidate.real_name,callback:function(e){t.$set(t.formValidate,"real_name",e)},expression:"formValidate.real_name"}})],1),e("el-form-item",{attrs:{label:"原始密码："}},[e("el-input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.pwd,callback:function(e){t.$set(t.formValidate,"pwd",e)},expression:"formValidate.pwd"}})],1),e("el-form-item",{attrs:{label:"新密码："}},[e("el-input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.new_pwd,callback:function(e){t.$set(t.formValidate,"new_pwd",e)},expression:"formValidate.new_pwd"}})],1),e("el-form-item",{attrs:{label:"确认新密码："}},[e("el-input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.conf_pwd,callback:function(e){t.$set(t.formValidate,"conf_pwd",e)},expression:"formValidate.conf_pwd"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),e("el-dialog",{attrs:{visible:t.avatarMoadl,title:"头像上传",width:"720px"},on:{"update:visible":function(e){t.avatarMoadl=e}}},[t.avatarMoadl?e("CropperImg",{on:{uploadImgSuccess:t.uploadImgSuccess}}):t._e()],1)],1)}),[],!1,null,"164d2104",null);e.default=r.exports},"3a12":function(t,e,o){"use strict";o("65ae")},"65ae":function(t,e,o){},"7e79":function(t,e,o){function r(t){var e=n[t];return void 0!==e||(e=n[t]={id:t,exports:{}},i[t](e,e.exports,r)),e.exports}var i,n,a;self,t.exports=(i={173:(t,e,o)=>{(t.exports=o(252)(!1)).push([t.id,'\n.vue-cropper[data-v-8ed66ddc] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  user-select: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  direction: ltr;\n  touch-action: none;\n  text-align: left;\n  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");\n}\n.cropper-box[data-v-8ed66ddc],\n.cropper-box-canvas[data-v-8ed66ddc],\n.cropper-drag-box[data-v-8ed66ddc],\n.cropper-crop-box[data-v-8ed66ddc],\n.cropper-face[data-v-8ed66ddc] {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  user-select: none;\n}\n.cropper-box-canvas img[data-v-8ed66ddc] {\n  position: relative;\n  text-align: left;\n  user-select: none;\n  transform: none;\n  max-width: none;\n  max-height: none;\n}\n.cropper-box[data-v-8ed66ddc] {\n  overflow: hidden;\n}\n.cropper-move[data-v-8ed66ddc] {\n  cursor: move;\n}\n.cropper-crop[data-v-8ed66ddc] {\n  cursor: crosshair;\n}\n.cropper-modal[data-v-8ed66ddc] {\n  background: rgba(0, 0, 0, 0.5);\n}\n.cropper-crop-box[data-v-8ed66ddc] {\n  /*border: 2px solid #39f;*/\n}\n.cropper-view-box[data-v-8ed66ddc] {\n  display: block;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  user-select: none;\n}\n.cropper-view-box img[data-v-8ed66ddc] {\n  user-select: none;\n  text-align: left;\n  max-width: none;\n  max-height: none;\n}\n.cropper-face[data-v-8ed66ddc] {\n  top: 0;\n  left: 0;\n  background-color: #fff;\n  opacity: 0.1;\n}\n.crop-info[data-v-8ed66ddc] {\n  position: absolute;\n  left: 0px;\n  min-width: 65px;\n  text-align: center;\n  color: white;\n  line-height: 20px;\n  background-color: rgba(0, 0, 0, 0.8);\n  font-size: 12px;\n}\n.crop-line[data-v-8ed66ddc] {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  opacity: 0.1;\n}\n.line-w[data-v-8ed66ddc] {\n  top: -3px;\n  left: 0;\n  height: 5px;\n  cursor: n-resize;\n}\n.line-a[data-v-8ed66ddc] {\n  top: 0;\n  left: -3px;\n  width: 5px;\n  cursor: w-resize;\n}\n.line-s[data-v-8ed66ddc] {\n  bottom: -3px;\n  left: 0;\n  height: 5px;\n  cursor: s-resize;\n}\n.line-d[data-v-8ed66ddc] {\n  top: 0;\n  right: -3px;\n  width: 5px;\n  cursor: e-resize;\n}\n.crop-point[data-v-8ed66ddc] {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  opacity: 0.75;\n  background-color: #39f;\n  border-radius: 100%;\n}\n.point1[data-v-8ed66ddc] {\n  top: -4px;\n  left: -4px;\n  cursor: nw-resize;\n}\n.point2[data-v-8ed66ddc] {\n  top: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: n-resize;\n}\n.point3[data-v-8ed66ddc] {\n  top: -4px;\n  right: -4px;\n  cursor: ne-resize;\n}\n.point4[data-v-8ed66ddc] {\n  top: 50%;\n  left: -4px;\n  margin-top: -3px;\n  cursor: w-resize;\n}\n.point5[data-v-8ed66ddc] {\n  top: 50%;\n  right: -4px;\n  margin-top: -3px;\n  cursor: e-resize;\n}\n.point6[data-v-8ed66ddc] {\n  bottom: -5px;\n  left: -4px;\n  cursor: sw-resize;\n}\n.point7[data-v-8ed66ddc] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: s-resize;\n}\n.point8[data-v-8ed66ddc] {\n  bottom: -5px;\n  right: -4px;\n  cursor: se-resize;\n}\n@media screen and (max-width: 500px) {\n.crop-point[data-v-8ed66ddc] {\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    opacity: 0.45;\n    background-color: #39f;\n    border-radius: 100%;\n}\n.point1[data-v-8ed66ddc] {\n    top: -10px;\n    left: -10px;\n}\n.point2[data-v-8ed66ddc],\n  .point4[data-v-8ed66ddc],\n  .point5[data-v-8ed66ddc],\n  .point7[data-v-8ed66ddc] {\n    display: none;\n}\n.point3[data-v-8ed66ddc] {\n    top: -10px;\n    right: -10px;\n}\n.point4[data-v-8ed66ddc] {\n    top: 0;\n    left: 0;\n}\n.point6[data-v-8ed66ddc] {\n    bottom: -10px;\n    left: -10px;\n}\n.point8[data-v-8ed66ddc] {\n    bottom: -10px;\n    right: -10px;\n}\n}\n',""])},252:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){r=e[1]||"";var o,r,i,n=(i=e[3])?(t&&"function"==typeof btoa?(o="/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */",n=i.sources.map((function(t){return"/*# sourceURL="+i.sourceRoot+t+" */"})),[r].concat(n).concat([o])):[r]).join("\n"):r;return e[2]?"@media "+e[2]+"{"+n+"}":n})).join("")},e.i=function(t,o){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},i=0;i<this.length;i++){var n=this[i][0];"number"==typeof n&&(r[n]=!0)}for(i=0;i<t.length;i++){var a=t[i];"number"==typeof a[0]&&r[a[0]]||(o&&!a[2]?a[2]=o:o&&(a[2]="("+a[2]+") and ("+o+")"),e.push(a))}},e}},125:(t,e,o)=>{var r=o(173);"string"==typeof r&&(r=[[t.id,r,""]]),o(723)(r,{hmr:!0,transform:void 0,insertInto:void 0}),r.locals&&(t.exports=r.locals)},723:(t,e,o)=>{var r,i,n,a={},s=(r=function(){return window&&document&&document.all&&!window.atob},n={},function(t,e){if("function"==typeof t)return t();if(void 0===n[t]){if(e=function(t,e){return(e||document).querySelector(t)}.call(this,t,e),window.HTMLIFrameElement&&e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}n[t]=e}return n[t]}),c=null,h=0,p=[],u=o(947);function l(t,e){for(var o=0;o<t.length;o++){var r=t[o],i=a[r.id];if(i){i.refs++;for(var n=0;n<i.parts.length;n++)i.parts[n](r.parts[n]);for(;n<r.parts.length;n++)i.parts.push(w(r.parts[n],e))}else{var s=[];for(n=0;n<r.parts.length;n++)s.push(w(r.parts[n],e));a[r.id]={id:r.id,refs:1,parts:s}}}}function d(t,e){for(var o=[],r={},i=0;i<t.length;i++){var n=t[i],a=e.base?n[0]+e.base:n[0];n={css:n[1],media:n[2],sourceMap:n[3]};r[a]?r[a].parts.push(n):o.push(r[a]={id:a,parts:[n]})}return o}function f(t,e){var o=s(t.insertInto);if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=p[p.length-1];if("top"===t.insertAt)r?r.nextSibling?o.insertBefore(e,r.nextSibling):o.appendChild(e):o.insertBefore(e,o.firstChild),p.push(e);else if("bottom"===t.insertAt)o.appendChild(e);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");r=s(t.insertAt.before,o),o.insertBefore(e,r)}}function g(t){null!==t.parentNode&&(t.parentNode.removeChild(t),0<=(t=p.indexOf(t)))&&p.splice(t,1)}function m(t){var e,r=document.createElement("style");return void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce&&(e=o.nc)&&(t.attrs.nonce=e),v(r,t.attrs),f(t,r),r}function v(t,e){Object.keys(e).forEach((function(o){t.setAttribute(o,e[o])}))}function w(t,e){var o,r,i,n,a;if(e.transform&&t.css){if(!(n="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=n}return i=e.singleton?(n=h++,o=c=c||m(e),r=C.bind(null,o,n,!1),C.bind(null,o,n,!0)):t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=e,a=document.createElement("link"),void 0===n.attrs.type&&(n.attrs.type="text/css"),n.attrs.rel="stylesheet",v(a,n.attrs),f(n,a),o=a,r=function(t,e,o){var r=o.css,i=(o=o.sourceMap,void 0===e.convertToAbsoluteUrls&&o);(e.convertToAbsoluteUrls||i)&&(r=u(r)),o&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e=new Blob([r],{type:"text/css"}),i=t.href;t.href=URL.createObjectURL(e),i&&URL.revokeObjectURL(i)}.bind(null,o,e),function(){g(o),o.href&&URL.revokeObjectURL(o.href)}):(o=m(e),r=function(t,e){var o=e.css;e=e.media;if(e&&t.setAttribute("media",e),t.styleSheet)t.styleSheet.cssText=o;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(o))}}.bind(null,o),function(){g(o)}),r(t),function(e){e?e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap||r(t=e):i()}}t.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"==typeof e.attrs?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=function(){return i=void 0===i?r.apply(this,arguments):i}()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var o=d(t,e);return l(o,e),function(t){for(var r,i=[],n=0;n<o.length;n++){var s=o[n];(r=a[s.id]).refs--,i.push(r)}for(t&&l(d(t,e),e),n=0;n<i.length;n++)if(0===(r=i[n]).refs){for(var c=0;c<r.parts.length;c++)r.parts[c]();delete a[r.id]}}},x=[];var x,b=function(t,e){return x[t]=e,x.filter(Boolean).join("\n")};function C(t,e,o,r){o=o?"":r.css;t.styleSheet?t.styleSheet.cssText=b(e,o):(r=document.createTextNode(o),(o=t.childNodes)[e]&&t.removeChild(o[e]),o.length?t.insertBefore(r,o[e]):t.appendChild(r))}},947:t=>{t.exports=function(t){var e,o,r="undefined"!=typeof window&&window.location;if(r)return t&&"string"==typeof t?(e=r.protocol+"//"+r.host,o=e+r.pathname.replace(/\/[^\/]*$/,"/"),t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,r){return r=r.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e})),/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(r)?t:(t=0===r.indexOf("//")?r:0===r.indexOf("/")?e+r:o+r.replace(/^\.\//,""),"url("+JSON.stringify(t)+")")}))):t;throw new Error("fixUrls requires window.location")}}},n={},r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0,a={},(()=>{"use strict";function t(){var t=this,e=t._self._c;return e("div",{ref:"cropper",staticClass:"vue-cropper",on:{mouseover:t.scaleImg,mouseout:t.cancelScale}},[t.imgs?e("div",{staticClass:"cropper-box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"cropper-box-canvas",style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}},[e("img",{ref:"cropperImg",attrs:{src:t.imgs,alt:"cropper-img"}})])]):t._e(),t._v(" "),e("div",{staticClass:"cropper-drag-box",class:{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping},on:{mousedown:t.startMove,touchstart:t.startMove}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.cropping,expression:"cropping"}],staticClass:"cropper-crop-box",style:{width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"}},[e("span",{staticClass:"cropper-view-box"},[e("img",{style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"},attrs:{src:t.imgs,alt:"cropper-img"}})]),t._v(" "),e("span",{staticClass:"cropper-face cropper-move",on:{mousedown:t.cropMove,touchstart:t.cropMove}}),t._v(" "),t.info?e("span",{staticClass:"crop-info",style:{top:t.cropInfo.top}},[t._v(t._s(t.cropInfo.width)+" × "+t._s(t.cropInfo.height))]):t._e(),t._v(" "),t.fixedBox?t._e():e("span",[e("span",{staticClass:"crop-line line-w",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-line line-a",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-line line-s",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-line line-d",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point1",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point2",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point3",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point4",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point5",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point6",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point7",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point8",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,2)}}})])])])}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,r=new Array(e);o<e;o++)r[o]=t[o];return r}function o(t,o){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var r,i,n,a,s=[],c=!0,h=!1;try{if(n=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;c=!1}else for(;!(c=(r=n.call(o)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){h=!0,i=t}finally{try{if(!c&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,o)||function(t,o){var r;if(t)return"string"==typeof t?e(t,o):"Map"===(r="Object"===(r=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(t,o):void 0}(t,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.r(a),r.d(a,{VueCropper:()=>u,default:()=>d}),t._withStripped=!0;var i,n,s,c={getData:function(t){return new Promise((function(e,o){var r,i,n={};r=t,i=null,new Promise((function(t,e){var o,n,a,s;r.src?/^data\:/i.test(r.src)?(i=function(t){t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var e=atob(t),o=e.length,r=(t=new ArrayBuffer(o),new Uint8Array(t)),i=0;i<o;i++)r[i]=e.charCodeAt(i);return t}(r.src),t(i)):/^blob\:/i.test(r.src)?((o=new FileReader).onload=function(e){i=e.target.result,t(i)},a=r.src,(s=new XMLHttpRequest).open("GET",a,!0),s.responseType="blob",s.onload=function(t){var e;200!=this.status&&0!==this.status||(e=this.response,o.readAsArrayBuffer(e))},s.send()):((n=new XMLHttpRequest).onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";i=n.response,t(i),n=null},n.open("GET",r.src,!0),n.responseType="arraybuffer",n.send(null)):e("img error")})).then((function(t){n.arrayBuffer=t,n.orientation=function(){var e,o,r,i,n,a,s,c,h=new DataView(t),p=h.byteLength;if(255===h.getUint8(0)&&216===h.getUint8(1))for(s=2;s<p;){if(255===h.getUint8(s)&&225===h.getUint8(s+1)){n=s;break}s++}if(a=n&&(o=n+10,"Exif"===function(t,e,o){var r="",i=e;for(o+=e;i<o;i++)r+=String.fromCharCode(t.getUint8(i));return r}(h,n+4,4))&&((r=18761===(i=h.getUint16(o)))||19789===i)&&42===h.getUint16(o+2,r)&&8<=(i=h.getUint32(o+4,r))?o+i:a)for(p=h.getUint16(a,r),c=0;c<p;c++)if(274===h.getUint16(s=a+12*c+2,r)){s+=8,e=h.getUint16(s,r);break}return e}(),e(n)})).catch((function(t){o(t)}))}))}};const h=c,p={data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:function(){return 10},validator:function(t){return Array.isArray(t)?0<=Number(t[0])&&0<=Number(t[1]):0<=Number(t)}}},computed:{cropInfo:function(){var t,e={};return e.top=21<this.cropOffsertY?"-21px":"0px",e.width=0<this.cropW?this.cropW:0,e.height=0<this.cropH?this.cropH:0,this.infoTrue&&(t=1,this.high&&!this.full&&(t=window.devicePixelRatio),1!==this.enlarge&!this.full&&(t=Math.abs(Number(this.enlarge))),e.width=e.width*t,e.height=e.height*t,this.full)&&(e.width=e.width/this.scale,e.height=e.height/this.scale),e.width=e.width.toFixed(0),e.height=e.height.toFixed(0),e},isIE:function(){return navigator.userAgent,!!window.ActiveXObject||"ActiveXObject"in window},passive:function(){return this.isIE?null:{passive:!1}}},watch:{img:function(){this.checkedImg()},imgs:function(t){""!==t&&this.reload()},cropW:function(){this.showPreview()},cropH:function(){this.showPreview()},cropOffsertX:function(){this.showPreview()},cropOffsertY:function(){this.showPreview()},scale:function(t,e){this.showPreview()},x:function(){this.showPreview()},y:function(){this.showPreview()},autoCrop:function(t){t&&this.goAutoCrop()},autoCropWidth:function(){this.autoCrop&&this.goAutoCrop()},autoCropHeight:function(){this.autoCrop&&this.goAutoCrop()},mode:function(){this.checkedImg()},rotate:function(){this.showPreview(),(this.autoCrop||0<this.cropW||0<this.cropH)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion:function(t){for(var e=navigator.userAgent.split(" "),o="",r=new RegExp(t,"i"),i=0;i<e.length;i++)r.test(e[i])&&(o=e[i]);return o?o.split("/")[1].split("."):["0","0","0"]},checkOrientationImage:function(t,e,o,r){var i,n=this,a=(81<=this.getVersion("chrome")[0]?e=-1:605<=this.getVersion("safari")[0]?13<(i=this.getVersion("version"))[0]&&1<i[1]&&(e=-1):(i=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/))&&(13<(i=(i=i[1]).split("_"))[0]||13<=i[0]&&4<=i[1])&&(e=-1),document.createElement("canvas")),s=a.getContext("2d");switch(s.save(),e){case 2:a.width=o,a.height=r,s.translate(o,0),s.scale(-1,1);break;case 3:a.width=o,a.height=r,s.translate(o/2,r/2),s.rotate(180*Math.PI/180),s.translate(-o/2,-r/2);break;case 4:a.width=o,a.height=r,s.translate(0,r),s.scale(1,-1);break;case 5:a.height=o,a.width=r,s.rotate(.5*Math.PI),s.scale(1,-1);break;case 6:a.width=r,a.height=o,s.translate(r/2,o/2),s.rotate(90*Math.PI/180),s.translate(-o/2,-r/2);break;case 7:a.height=o,a.width=r,s.rotate(.5*Math.PI),s.translate(o,-r),s.scale(-1,1);break;case 8:a.height=o,a.width=r,s.translate(r/2,o/2),s.rotate(-90*Math.PI/180),s.translate(-o/2,-r/2);break;default:a.width=o,a.height=r}s.drawImage(t,0,0,o,r),s.restore(),a.toBlob((function(t){t=URL.createObjectURL(t),URL.revokeObjectURL(n.imgs),n.imgs=t}),"image/"+this.outputType,1)},checkedImg:function(){var t,e,o=this;null===this.img||""===this.img?(this.imgs="",this.clearCrop()):(this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop(),(t=new Image).onload=function(){if(""===o.img)return o.$emit("imgLoad","error"),o.$emit("img-load","error"),!1;var e=t.width,r=t.height;h.getData(t).then((function(i){o.orientation=i.orientation||1,i=Number(o.maxImgSize),!o.orientation&&e<i&r<i?o.imgs=o.img:(i<e&&(r=r/e*i,e=i),i<r&&(e=e/r*i,r=i),o.checkOrientationImage(t,o.orientation,e,r))}))},t.onerror=function(){o.$emit("imgLoad","error"),o.$emit("img-load","error")},"data"!==this.img.substr(0,4)&&(t.crossOrigin=""),this.isIE?((e=new XMLHttpRequest).onload=function(){var e=URL.createObjectURL(this.response);t.src=e},e.open("GET",this.img,!0),e.responseType="blob",e.send()):t.src=this.img)},startMove:function(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t:t.touches[0]).clientX-this.x,this.moveY=("clientY"in t?t:t.touches[0]).clientY-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX||t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY||t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX=("clientX"in t?t:t.touches[0]).clientX,this.cropY=("clientY"in t?t:t.touches[0]).clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale:function(t){var e=this,o=(t.preventDefault(),this.scale),r=this.touches[0].clientX,i=this.touches[0].clientY,n=t.touches[0].clientX,a=t.touches[0].clientY,s=this.touches[1].clientX,c=this.touches[1].clientY,h=t.touches[1].clientX,p=t.touches[1].clientY;r=Math.sqrt(Math.pow(r-s,2)+Math.pow(i-c,2)),s=Math.sqrt(Math.pow(n-h,2)+Math.pow(a-p,2))-r,c=(.1<(i=1/this.trueWidth>1/this.trueHeight?1/this.trueHeight:1/this.trueWidth)?.1:i)*s;if(!this.touchNow){if(this.touchNow=!0,0<s?o+=Math.abs(c):s<0&&o>Math.abs(c)&&(o-=Math.abs(c)),this.touches=t.touches,setTimeout((function(){e.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o}},cancelTouchScale:function(t){window.removeEventListener("touchmove",this.touchScale)},moveImg:function(t){var e=this;if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;var o=("clientX"in t?t:t.touches[0]).clientX,r=(t=("clientY"in t?t:t.touches[0]).clientY,o-this.moveX),i=t-this.moveY;this.$nextTick((function(){if(e.centerBox){var t,o,n,a,s=e.getImgAxis(r,i,e.scale),c=e.getCropAxis(),h=e.trueHeight*e.scale,p=e.trueWidth*e.scale;switch(e.rotate){case 1:case-1:case 3:case-3:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2+(h-p)/2,o=e.cropOffsertY-e.trueHeight*(1-e.scale)/2+(p-h)/2,n=t-h+e.cropW,a=o-p+e.cropH;break;default:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2,o=e.cropOffsertY-e.trueHeight*(1-e.scale)/2,n=t-p+e.cropW,a=o-h+e.cropH}s.x1>=c.x1&&(r=t),s.y1>=c.y1&&(i=o),s.x2<=c.x2&&(r=n),s.y2<=c.y2&&(i=a)}e.x=r,e.y=i,e.$emit("imgMoving",{moving:!0,axis:e.getImgAxis()}),e.$emit("img-moving",{moving:!0,axis:e.getImgAxis()})}))},leaveImg:function(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("imgMoving",{moving:!1,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg:function(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale:function(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize:function(t){var e=this,o=(t.preventDefault(),this.scale),r=(t=t.deltaY||t.wheelDelta,t=0<navigator.userAgent.indexOf("Firefox")?30*t:t,this.isIE&&(t=-t),this.coe);r=(r/this.trueWidth>r/this.trueHeight?r/this.trueHeight:r/this.trueWidth)*t,r<0?o+=Math.abs(r):o>Math.abs(r)&&(o-=Math.abs(r)),t=r<0?"add":"reduce";if(t!==this.coeStatus&&(this.coeStatus=t,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((function(){e.scaling=!1,e.coe=e.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o},changeScale:function(t){var e=this.scale;if(t=t||1,0<(t*=20/this.trueWidth>20/this.trueHeight?20/this.trueHeight:20/this.trueWidth)?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop:function(t){var e=this,o=(t.preventDefault(),"clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0),r="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((function(){var t,i=o-e.cropX,n=r-e.cropY;0<i?(e.cropW=i+e.cropChangeX>e.w?e.w-e.cropChangeX:i,e.cropOffsertX=e.cropChangeX):(e.cropW=e.w-e.cropChangeX+Math.abs(i)>e.w?e.cropChangeX:Math.abs(i),e.cropOffsertX=0<e.cropChangeX+i?e.cropChangeX+i:0),e.fixed?((t=e.cropW/e.fixedNumber[0]*e.fixedNumber[1])+e.cropOffsertY>e.h?(e.cropH=e.h-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],e.cropOffsertX=0<i?e.cropChangeX:e.cropChangeX-e.cropW):e.cropH=t,e.cropOffsertY=e.cropOffsertY):0<n?(e.cropH=n+e.cropChangeY>e.h?e.h-e.cropChangeY:n,e.cropOffsertY=e.cropChangeY):(e.cropH=e.h-e.cropChangeY+Math.abs(n)>e.h?e.cropChangeY:Math.abs(n),e.cropOffsertY=0<e.cropChangeY+n?e.cropChangeY+n:0)}))},changeCropSize:function(t,e,o,r,i){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=o,this.changeCropTypeX=r,this.changeCropTypeY=i,this.cropX=("clientX"in t?t:t.touches[0]).clientX,this.cropY=("clientY"in t?t:t.touches[0]).clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("changeCropSize",{width:this.cropW,height:this.cropH}),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow:function(t){var e,r,i=this,n=(t.preventDefault(),"clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0),a="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0,s=this.w,c=this.h,h=0,p=0,u=(t=(this.centerBox&&(e=(t=this.getImgAxis()).x2,r=t.y2,h=0<t.x1?t.x1:0,p=0<t.y1?t.y1:0,e<s&&(s=e),r<c)&&(c=r),o(this.checkCropLimitSize(),2)),t[0]),l=t[1];this.$nextTick((function(){var t=n-i.cropX,e=a-i.cropY;i.canChangeX&&(1===i.changeCropTypeX?i.cropOldW-t<u?(i.cropW=u,i.cropOffsertX=i.cropOldW+i.cropChangeX-h-u):0<i.cropOldW-t?(i.cropW=s-i.cropChangeX-t<=s-h?i.cropOldW-t:i.cropOldW+i.cropChangeX-h,i.cropOffsertX=s-i.cropChangeX-t<=s-h?i.cropChangeX+t:h):(i.cropW=Math.abs(t)+i.cropChangeX<=s?Math.abs(t)-i.cropOldW:s-i.cropOldW-i.cropChangeX,i.cropOffsertX=i.cropChangeX+i.cropOldW):2===i.changeCropTypeX&&(i.cropOldW+t<u?i.cropW=u:0<i.cropOldW+t?(i.cropW=i.cropOldW+t+i.cropOffsertX<=s?i.cropOldW+t:s-i.cropOffsertX,i.cropOffsertX=i.cropChangeX):(i.cropW=s-i.cropChangeX+Math.abs(t+i.cropOldW)<=s-h?Math.abs(t+i.cropOldW):i.cropChangeX-h,i.cropOffsertX=s-i.cropChangeX+Math.abs(t+i.cropOldW)<=s-h?i.cropChangeX-Math.abs(t+i.cropOldW):h))),i.canChangeY&&(1===i.changeCropTypeY?i.cropOldH-e<l?(i.cropH=l,i.cropOffsertY=i.cropOldH+i.cropChangeY-p-l):0<i.cropOldH-e?(i.cropH=c-i.cropChangeY-e<=c-p?i.cropOldH-e:i.cropOldH+i.cropChangeY-p,i.cropOffsertY=c-i.cropChangeY-e<=c-p?i.cropChangeY+e:p):(i.cropH=Math.abs(e)+i.cropChangeY<=c?Math.abs(e)-i.cropOldH:c-i.cropOldH-i.cropChangeY,i.cropOffsertY=i.cropChangeY+i.cropOldH):2===i.changeCropTypeY&&(i.cropOldH+e<l?i.cropH=l:0<i.cropOldH+e?(i.cropH=i.cropOldH+e+i.cropOffsertY<=c?i.cropOldH+e:c-i.cropOffsertY,i.cropOffsertY=i.cropChangeY):(i.cropH=c-i.cropChangeY+Math.abs(e+i.cropOldH)<=c-p?Math.abs(e+i.cropOldH):i.cropChangeY-p,i.cropOffsertY=c-i.cropChangeY+Math.abs(e+i.cropOldH)<=c-p?i.cropChangeY-Math.abs(e+i.cropOldH):p))),i.canChangeX&&i.fixed&&((t=i.cropW/i.fixedNumber[0]*i.fixedNumber[1])<l?(i.cropH=l,i.cropW=i.fixedNumber[0]*l/i.fixedNumber[1],1===i.changeCropTypeX&&(i.cropOffsertX=i.cropChangeX+(i.cropOldW-i.cropW))):t+i.cropOffsertY>c?(i.cropH=c-i.cropOffsertY,i.cropW=i.cropH/i.fixedNumber[1]*i.fixedNumber[0],1===i.changeCropTypeX&&(i.cropOffsertX=i.cropChangeX+(i.cropOldW-i.cropW))):i.cropH=t),i.canChangeY&&i.fixed&&((e=i.cropH/i.fixedNumber[1]*i.fixedNumber[0])<u?(i.cropW=u,i.cropH=i.fixedNumber[1]*u/i.fixedNumber[0]):e+i.cropOffsertX>s?(i.cropW=s-i.cropOffsertX,i.cropH=i.cropW/i.fixedNumber[0]*i.fixedNumber[1]):i.cropW=e),i.$emit("cropSizing",{cropW:i.cropW,cropH:i.cropH}),i.$emit("crop-sizing",{cropW:i.cropW,cropH:i.cropH})}))},checkCropLimitSize:function(){this.cropW,this.cropH;var t=this.limitMinSize;new Array,t=Array.isArray(t)?t:[t,t];return[parseFloat(t[0]),parseFloat(t[1])]},changeCropEnd:function(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize:function(t,e,o,r,i,n){t/=e,e=i;var a=n;return e<o&&(e=o,a=Math.ceil(e/t)),a<r&&(a=r,(e=Math.ceil(a*t))<o)&&(e=o,a=Math.ceil(e/t)),e<i&&(e=i,a=Math.ceil(e/t)),a<n&&(a=n,e=Math.ceil(a*t)),{width:e,height:a}},endCrop:function(){0===this.cropW&&0===this.cropH&&(this.cropping=!1);var t=o(this.checkCropLimitSize(),2),e=t[0];t=t[1],e=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],e,t,this.cropW,this.cropH):{width:e,height:t},t=e.width,e=e.height;t>this.cropW&&(this.cropW=t,this.cropOffsertX+t>this.w)&&(this.cropOffsertX=this.w-t),e>this.cropH&&(this.cropH=e,this.cropOffsertY+e>this.h)&&(this.cropOffsertY=this.h-e),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop:function(){this.crop=!0},stopCrop:function(){this.crop=!1},clearCrop:function(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove:function(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);var e=("clientX"in t?t:t.touches[0]).clientX;t=("clientY"in t?t:t.touches[0]).clientY,e-=this.cropOffsertX,t-=this.cropOffsertY;this.cropX=e,this.cropY=t,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop:function(t,e){var o=this,r=0,i=0;t&&(t.preventDefault(),r=("clientX"in t?t:t.touches[0]).clientX,i=("clientY"in t?t:t.touches[0]).clientY),this.$nextTick((function(){var t,n=r-o.cropX,a=i-o.cropY;e&&(n=o.cropOffsertX,a=o.cropOffsertY),n=n<=0?0:n+o.cropW>o.w?o.w-o.cropW:n,a=a<=0?0:a+o.cropH>o.h?o.h-o.cropH:a,o.centerBox&&((n=n<=(t=o.getImgAxis()).x1?t.x1:n)+o.cropW>t.x2&&(n=t.x2-o.cropW),(a=a<=t.y1?t.y1:a)+o.cropH>t.y2)&&(a=t.y2-o.cropH),o.cropOffsertX=n,o.cropOffsertY=a,o.$emit("cropMoving",{moving:!0,axis:o.getCropAxis()}),o.$emit("crop-moving",{moving:!0,axis:o.getCropAxis()})}))},getImgAxis:function(t,e,o){t=t||this.x,e=e||this.y,o=o||this.scale;var r={x1:0,x2:0,y1:0,y2:0},i=this.trueWidth*o,n=this.trueHeight*o;switch(this.rotate){case 0:r.x1=t+this.trueWidth*(1-o)/2,r.x2=r.x1+this.trueWidth*o,r.y1=e+this.trueHeight*(1-o)/2,r.y2=r.y1+this.trueHeight*o;break;case 1:case-1:case 3:case-3:r.x1=t+this.trueWidth*(1-o)/2+(i-n)/2,r.x2=r.x1+this.trueHeight*o,r.y1=e+this.trueHeight*(1-o)/2+(n-i)/2,r.y2=r.y1+this.trueWidth*o;break;default:r.x1=t+this.trueWidth*(1-o)/2,r.x2=r.x1+this.trueWidth*o,r.y1=e+this.trueHeight*(1-o)/2,r.y2=r.y1+this.trueHeight*o}return r},getCropAxis:function(){var t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop:function(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("cropMoving",{moving:!1,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked:function(t){var e=this,o=document.createElement("canvas"),r=new Image,i=this.rotate,n=this.trueWidth,a=this.trueHeight,s=this.cropOffsertX,c=this.cropOffsertY;function h(t,e){o.width=Math.round(t),o.height=Math.round(e)}r.onload=function(){if(0!==e.cropW){var p=o.getContext("2d"),u=1,l=(e.high&!e.full&&(u=window.devicePixelRatio),1!==e.enlarge&!e.full&&(u=Math.abs(Number(e.enlarge))),e.cropW*u),d=e.cropH*u,f=n*e.scale*u,g=a*e.scale*u,m=(e.x-s+e.trueWidth*(1-e.scale)/2)*u,v=(e.y-c+e.trueHeight*(1-e.scale)/2)*u;switch(h(l,d),p.save(),i){case 0:e.full?(h(l/e.scale,d/e.scale),p.drawImage(r,m/e.scale,v/e.scale,f/e.scale,g/e.scale)):p.drawImage(r,m,v,f,g);break;case 1:case-3:e.full?(h(l/e.scale,d/e.scale),m=m/e.scale+(f/e.scale-g/e.scale)/2,v=v/e.scale+(g/e.scale-f/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(r,v,-m-g/e.scale,f/e.scale,g/e.scale)):(m+=(f-g)/2,v+=(g-f)/2,p.rotate(90*i*Math.PI/180),p.drawImage(r,v,-m-g,f,g));break;case 2:case-2:e.full?(h(l/e.scale,d/e.scale),p.rotate(90*i*Math.PI/180),m/=e.scale,v/=e.scale,p.drawImage(r,-m-f/e.scale,-v-g/e.scale,f/e.scale,g/e.scale)):(p.rotate(90*i*Math.PI/180),p.drawImage(r,-m-f,-v-g,f,g));break;case 3:case-1:e.full?(h(l/e.scale,d/e.scale),m=m/e.scale+(f/e.scale-g/e.scale)/2,v=v/e.scale+(g/e.scale-f/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(r,-v-f/e.scale,m,f/e.scale,g/e.scale)):(m+=(f-g)/2,v+=(g-f)/2,p.rotate(90*i*Math.PI/180),p.drawImage(r,-v-f,m,f,g));break;default:e.full?(h(l/e.scale,d/e.scale),p.drawImage(r,m/e.scale,v/e.scale,f/e.scale,g/e.scale)):p.drawImage(r,m,v,f,g)}p.restore()}else{var w=n*e.scale,x=a*e.scale,b=o.getContext("2d");switch(b.save(),i){case 0:h(w,x),b.drawImage(r,0,0,w,x);break;case 1:case-3:h(x,w),b.rotate(90*i*Math.PI/180),b.drawImage(r,0,-x,w,x);break;case 2:case-2:h(w,x),b.rotate(90*i*Math.PI/180),b.drawImage(r,-w,-x,w,x);break;case 3:case-1:h(x,w),b.rotate(90*i*Math.PI/180),b.drawImage(r,-w,0,w,x);break;default:h(w,x),b.drawImage(r,0,0,w,x)}b.restore()}t(o)},"data"!==this.img.substr(0,4)&&(r.crossOrigin="Anonymous"),r.src=this.imgs},getCropData:function(t){var e=this;this.getCropChecked((function(o){t(o.toDataURL("image/"+e.outputType,e.outputSize))}))},getCropBlob:function(t){var e=this;this.getCropChecked((function(o){o.toBlob((function(e){return t(e)}),"image/"+e.outputType,e.outputSize)}))},showPreview:function(){var t=this;if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((function(){t.isCanShow=!0}),16);var e=this.cropW,o=this.cropH,r=this.scale,i={},n=(i.div={width:"".concat(e,"px"),height:"".concat(o,"px")},(this.x-this.cropOffsertX)/r),a=(this.y-this.cropOffsertY)/r;i.w=e,i.h=o,i.url=this.imgs,i.img={width:"".concat(this.trueWidth,"px"),height:"".concat(this.trueHeight,"px"),transform:"scale(".concat(r,")translate3d(").concat(n,"px, ").concat(a,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,"deg)")},i.html='\n      <div class="show-preview" style="width: '.concat(i.w,"px; height: ").concat(i.h,'px; overflow: hidden">\n        <div style="width: ').concat(e,"px; height: ").concat(o,'px">\n          <img src=').concat(i.url,' style="width: ').concat(this.trueWidth,"px; height: ").concat(this.trueHeight,"px; transform:\n          scale(").concat(r,")translate3d(").concat(n,"px, ").concat(a,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,'deg)">\n        </div>\n      </div>'),this.$emit("realTime",i),this.$emit("real-time",i)},reload:function(){var t=this,e=new Image;e.onload=function(){t.w=parseFloat(window.getComputedStyle(t.$refs.cropper).width),t.h=parseFloat(window.getComputedStyle(t.$refs.cropper).height),t.trueWidth=e.width,t.trueHeight=e.height,t.original?t.scale=1:t.scale=t.checkedMode(),t.$nextTick((function(){t.x=-(t.trueWidth-t.trueWidth*t.scale)/2+(t.w-t.trueWidth*t.scale)/2,t.y=-(t.trueHeight-t.trueHeight*t.scale)/2+(t.h-t.trueHeight*t.scale)/2,t.loading=!1,t.autoCrop&&t.goAutoCrop(),t.$emit("img-load","success"),t.$emit("imgLoad","success"),setTimeout((function(){t.showPreview()}),20)}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},e.src=this.imgs},checkedMode:function(){var t=1,e=(this.trueWidth,this.trueHeight),o=this.mode.split(" ");switch(o[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":(e*=t=this.w/this.trueWidth)<this.h&&(t=(e=this.h)/this.trueHeight);break;default:try{var r,i,n,a,s=o[0];-1!==s.search("px")&&(s=s.replace("px",""),r=parseFloat(s)/this.trueWidth,-(i=1)!==(n=o[1]).search("px")&&(n=n.replace("px",""),i=(e=parseFloat(n))/this.trueHeight),t=Math.min(r,i)),-1!==s.search("%")&&(s=s.replace("%",""),t=parseFloat(s)/100*this.w/this.trueWidth),2===o.length&&"auto"===s&&(-1!==(a=o[1]).search("px")&&(a=a.replace("px",""),t=(e=parseFloat(a))/this.trueHeight),-1!==a.search("%"))&&(a=a.replace("%",""),t=(e=parseFloat(a)/100*this.h)/this.trueHeight)}catch(e){t=1}}return t},goAutoCrop:function(t,e){var o,r,i,n;""!==this.imgs&&null!==this.imgs&&(this.clearCrop(),this.cropping=!0,o=this.w,r=this.h,this.centerBox&&(o=(n=((i=0<Math.abs(this.rotate)%2)?this.trueHeight:this.trueWidth)*this.scale)<o?n:o,r=(n=(i?this.trueWidth:this.trueHeight)*this.scale)<r?n:r),i=t||parseFloat(this.autoCropWidth),n=e||parseFloat(this.autoCropHeight),0!==i&&0!==n||(i=.8*o,n=.8*r),i=o<i?o:i,n=r<n?r:n,(n=this.fixed?i/this.fixedNumber[0]*this.fixedNumber[1]:n)>this.h&&(i=(n=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(i,n))},changeCrop:function(t,e){var o,r=this;this.centerBox&&(e=t>(o=this.getImgAxis()).x2-o.x1?(t=o.x2-o.x1)/this.fixedNumber[0]*this.fixedNumber[1]:e)>o.y2-o.y1&&(t=(e=o.y2-o.y1)/this.fixedNumber[1]*this.fixedNumber[0]),this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((function(){r.cropOffsertX=(r.w-r.cropW)/2,r.cropOffsertY=(r.h-r.cropH)/2,r.centerBox&&r.moveCrop(null,!0)}))},refresh:function(){var t=this;this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick((function(){t.checkedImg()}))},rotateLeft:function(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight:function(){this.rotate=3<=this.rotate?0:this.rotate+1},rotateClear:function(){this.rotate=0},checkoutImgAxis:function(t,e,o){t=t||this.x,e=e||this.y,o=o||this.scale;var r=!0;return!(this.centerBox&&(t=this.getImgAxis(t,e,o),e=this.getCropAxis(),t.x1>=e.x1&&(r=!1),t.x2<=e.x2&&(r=!1),t.y1>=e.y1&&(r=!1),t.y2<=e.y2))&&r}},mounted:function(){this.support="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";var t=this,e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,o,r){for(var i=atob(this.toDataURL(o,r).split(",")[1]),n=i.length,a=new Uint8Array(n),s=0;s<n;s++)a[s]=i.charCodeAt(s);e(new Blob([a],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},destroyed:function(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}},u=(r(125),c=t,n="8ed66ddc",(s="function"==typeof(i=p)?i.options:i).render=c,s.staticRenderFns=[],s._compiled=!0,s._scopeId="data-v-"+n,{exports:i,options:s}.exports);function l(t){t.component("VueCropper",u)}"undefined"!=typeof window&&window.Vue&&l(window.Vue);const d={version:"0.5.11",install:l,VueCropper:u,vueCropper:u}})(),a)},c7cd:function(t,e,o){"use strict";var r=o("23e7"),i=o("857a");r({target:"String",proto:!0,forced:o("af03")("fixed")},{fixed:function(){return i(this,"tt","","")}})},cdfe:function(t,e,o){t.exports=o.p+"system_static/img/f.5aa43cd3.png"},e8be:function(t,e,o){}}]);