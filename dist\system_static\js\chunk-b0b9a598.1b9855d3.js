(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-b0b9a598"],{"09dd":function(t,e,i){"use strict";i.d(e,"e",(function(){return n})),i.d(e,"b",(function(){return a})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"d",(function(){return l})),i.d(e,"i",(function(){return c})),i.d(e,"g",(function(){return d})),i.d(e,"h",(function(){return u})),i.d(e,"f",(function(){return m})),i("99af");var s=i("6b6c");function n(t){return Object(s.a)({url:"/sign/list",method:"get",params:t})}function a(t){return Object(s.a)({url:"marketing/sign/delete/".concat(t),method:"DELETE"})}function r(t,e){return Object(s.a)({url:"marketing/sign/set_status/".concat(t,"/").concat(e),method:"PUT"})}function o(t){return Object(s.a)({url:"sign/addInfo",method:"get",params:{id:t}}).then((function(t){var e;return t.data&&t.data.config&&t.data.config.formData&&((e=t.data.config.formData).start_time&&(e.start_time=String(e.start_time)),e.end_time)&&(e.end_time=String(e.end_time)),t}))}function l(t){return Object(s.a)({url:"sign/info/".concat(t),method:"get"})}function c(t){return Object(s.a)({url:"sign/member_sign",method:"post",data:t})}function d(t){return Object(s.a)({url:"sign/signed_members/".concat(t),method:"get"})}function u(t){return Object(s.a)({url:"sign/unsigned_members/".concat(t),method:"get"})}function m(t){return Object(s.a)({url:"sign/record/list",method:"get",params:t})}},"1dd5":function(t,e,i){"use strict";i("68df")},"232f":function(t,e,i){"use strict";i("a9e3"),i("d81d"),i("d3b7"),i("159b"),i("caad"),i("2532"),i("14d9"),i("a434");var s=i("f6b0"),n={name:"userLabel",props:{uid:{type:String|Number,default:0},only_get:{default:!1},selectDataLabel:{type:Array,default:function(){}}},data:function(){return{labelList:[],activeIds:[],unLaberids:[]}},watch:{uid:{handler:function(t,e){t!=e&&this.getList()},deep:!0}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Object(s.e)(this.uid||0).then((function(e){var i;t.labelList=e.data.list,t.activeIds=[],t.selectDataLabel&&t.selectDataLabel.length?(i=t.selectDataLabel.map((function(t){return t.id})),t.labelList.forEach((function(e){i.includes(e.id)?(e.disabled=!0,t.activeIds.push(e.id)):e.disabled=!1}))):t.labelList.forEach((function(t){t.disabled=!1}))}))},selectLabel:function(t){var e;t.disabled?(e=this.activeIds.indexOf(t.id),this.activeIds.splice(e,1),t.disabled=!1):(this.activeIds.push(t.id),t.disabled=!0),this.$forceUpdate()},subBtn:function(){var t=[];this.only_get?(this.labelList.forEach((function(e){!0===e.disabled&&t.push({id:e.id,label_name:e.tag_name})})),this.$emit("activeData",t)):(this.labelList.forEach((function(e){e.label&&Array.isArray(e.label)&&e.label.forEach((function(e){!1===e.disabled&&t.push(e.id)}))})),this.unLaberids=t,this.$emit("close"))},cancel:function(){this.activeIds=[],this.unLaberids=[],this.$emit("close")}}};i("b644"),i=i("2877"),i=Object(i.a)(n,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"label-wrapper"},[t.labelList[0]?e("div",{staticClass:"label-box"},[e("div",{staticClass:"list"},t._l(t.labelList,(function(i,s){return e("div",{key:s,staticClass:"label-item",class:{on:i.disabled},on:{click:function(e){return t.selectLabel(i)}}},[t._v("\n        "+t._s(i.tag_name)+"\n      ")])})),0)]):e("div",{staticClass:"nonefont"},[t._v("暂无标签")]),e("div",{staticClass:"acea-row row-right mt20"},[e("el-button",{on:{click:t.cancel}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确 定")])],1)])}),[],!1,null,"dae683f8",null);e.a=i.exports},"68df":function(t,e,i){},"80d0":function(t,e,i){},"9fca":function(t,e,i){"use strict";i.r(e),i("b0c0");var s=i("2909"),n=i("5530"),a=(i("a434"),i("99af"),i("d81d"),i("a15b"),i("d3b7"),i("159b"),i("7db0"),i("4de4"),i("caad"),i("2532"),i("09dd")),r=i("f6b0"),o=i("b199"),l=i("333d"),c=i("232f");l={name:"sign_list",components:{pagination:l.a,userLabel:c.a},data:function(){return{loading:!1,tableList:[],total:0,listQuery:{page:1,limit:15},dialogVisible:!1,dialogTitle:"添加签到",formData:{id:0,title:"",integral:0,start_time:"",end_time:"",status:1},timeRange:[],rules:{title:[{required:!0,message:"请输入签到标题",trigger:"blur"}],integral:[{required:!0,message:"请输入赠送积分",trigger:"blur"}]},signDialogVisible:!1,currentSign:{id:0,title:"",start_time:"",end_time:"",start_time_formatted:"",end_time_formatted:""},signStartTime:"",cardNumber:"",signedMembers:[],unsignedMembers:[],signTimer:null,autoSubmitTimer:null,remainingTime:"00:00:00",countdownTimer:null,focusTimer:null,addCustomerDialogVisible:!1,currentSignId:0,filterForm:{representative:"",label_ids:"",task_ids:""},representativeOptions:[],selectedLabels:[],selectedTasks:[],labelDialogVisible:!1,taskDialogVisible:!1,taskList:[],taskLoading:!1,selectedTaskIds:[],tempSelectedTasks:[],filteredCustomers:[],customerLoading:!1,customerListQuery:{representative:"",label_ids:"",task_ids:"",sign_id:""},selectedCustomerIds:[],selectedCustomers:[],confirmLoading:!1,addedCustomersDialogVisible:!1,addedCustomersLoading:!1,updateCustomersLoading:!1,addedCustomers:[],selectedAddedCustomerIds:[],currentAddedSignId:0}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(a.e)(this.listQuery).then((function(e){t.tableList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.msg||"获取签到列表失败")}))},add:function(){var t=this;this.$modalForm(Object(a.c)(0)).then((function(){t.getList()})).catch((function(e){t.$message.error(e.msg||"添加签到失败")}))},addCustrom:function(t){var e=this;this.currentSignId=t.id,this.customerListQuery.sign_id=t.id,this.resetAddCustomerForm(),Object(r.j)({sign_id:t.id}).then((function(t){e.representativeOptions=t.data.list})),this.addCustomerDialogVisible=!0,this.getCustomerList()},edit:function(t){var e=this;this.$modalForm(Object(a.c)(t.id)).then((function(){e.getList()}))},del:function(t,e,i){var s=this;this.$confirm("确定要".concat(e,"吗?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(a.b)(t.id).then((function(t){s.$message.success(t.msg||"删除成功"),s.tableList.splice(i,1),0===s.tableList.length&&1<s.listQuery.page&&(s.listQuery.page--,s.getList())})).catch((function(t){s.$message.error(t.msg||"删除失败")}))})).catch((function(){s.$message.info("已取消删除")}))},onchangeIsShow:function(t){var e=this;Object(a.a)(t.id,t.status).then((function(t){e.$message.success(t.msg||"状态修改成功")})).catch((function(i){t.status=0===t.status?1:0,e.$message.error(i.msg||"状态修改失败")}))},startSign:function(t){var e=this;this.currentSign=Object(n.a)({},t),this.signStartTime=this.$moment().format("YYYY-MM-DD HH:mm:ss"),this.cardNumber="",this.signedMembers=[],this.unsignedMembers=[],Object(a.d)(t.id).then((function(t){t.data&&(t.data.start_time&&(e.currentSign.start_time=t.data.start_time),t.data.end_time)&&(e.currentSign.end_time=t.data.end_time),e.getSignedMembersList(),e.getUnsignedMembersList(),e.signDialogVisible=!0,e.startRefreshTimer(),e.startCountdown(),e.$nextTick((function(){e.$refs.cardInput.focus()})),e.startAutoFocusTimer()})).catch((function(t){e.$message.error(t.msg||"获取签到活动详情失败")}))},startAutoFocusTimer:function(){var t=this;this.focusTimer&&clearInterval(this.focusTimer),this.focusTimer=setInterval((function(){t.signDialogVisible&&t.$refs.cardInput?document.activeElement!==t.$refs.cardInput.$el.querySelector("input")&&t.$refs.cardInput.focus():(clearInterval(t.focusTimer),t.focusTimer=null)}),5e3)},startCountdown:function(){function t(){var t,n,a=e.$moment().valueOf();a<i?e.remainingTime="活动未开始":s<a?(e.remainingTime="0小时0分钟0秒 (已结束)",clearInterval(e.countdownTimer)):(a=e.$moment.duration(s-a),t=Math.floor(a.asHours()),n=a.minutes(),a=a.seconds(),e.remainingTime="".concat(t,"小时").concat(n,"分钟").concat(a,"秒"))}var e=this,i=(this.countdownTimer&&clearInterval(this.countdownTimer),this.currentSign.start_time?1e3*parseInt(this.currentSign.start_time):0),s=this.currentSign.end_time?1e3*parseInt(this.currentSign.end_time):0;this.currentSign.start_time_formatted=i?this.$moment(i).format("YYYY-MM-DD HH:mm:ss"):"",this.currentSign.end_time_formatted=s?this.$moment(s).format("YYYY-MM-DD HH:mm:ss"):"",t(),this.countdownTimer=setInterval(t,1e3)},getSignedMembersList:function(){var t=this;Object(a.g)(this.currentSign.id).then((function(e){t.signedMembers=e.data.list||[]})).catch((function(e){t.$message.error(e.msg||"获取已签到会员列表失败")}))},getUnsignedMembersList:function(){var t=this;Object(a.h)(this.currentSign.id).then((function(e){t.unsignedMembers=e.data.list||[]})).catch((function(e){t.$message.error(e.msg||"获取待签到会员列表失败")}))},handleCardInput:function(t){var e=this;this.autoSubmitTimer&&(clearTimeout(this.autoSubmitTimer),this.autoSubmitTimer=null),t&&8===t.length&&(this.autoSubmitTimer=setTimeout((function(){e.submitSign()}),500))},submitSign:function(){var t,e=this;this.cardNumber?(t={sign_id:this.currentSign.id,card_no:this.cardNumber},Object(a.i)(t).then((function(t){e.$message.success(t.msg||"签到成功"),e.cardNumber="",e.getSignedMembersList(),e.getUnsignedMembersList(),e.$nextTick((function(){e.$refs.cardInput.focus()}))})).catch((function(t){e.$message.error(t.msg||"签到失败"),e.$nextTick((function(){e.$refs.cardInput.focus()}))}))):this.$message.warning("请输入会员卡号")},startRefreshTimer:function(){var t=this;this.signTimer&&clearInterval(this.signTimer),this.signTimer=setInterval((function(){t.signDialogVisible?(t.getSignedMembersList(),t.getUnsignedMembersList()):(clearInterval(t.signTimer),t.signTimer=null)}),3e4)},handleInputBlur:function(){},handleSignDialogClosed:function(){this.countdownTimer&&(clearInterval(this.countdownTimer),this.countdownTimer=null),this.signTimer&&(clearInterval(this.signTimer),this.signTimer=null),this.focusTimer&&(clearTimeout(this.focusTimer),this.focusTimer=null)},resetAddCustomerForm:function(){this.filterForm={representative:"",label_ids:"",task_ids:""},this.selectedLabels=[],this.selectedTasks=[],this.selectedCustomerIds=[],this.selectedCustomers=[],this.customerListQuery={representative:"",label_ids:"",task_ids:"",sign_id:""}},handleAddCustomerDialogClosed:function(){this.resetAddCustomerForm()},openSelectLabel:function(){this.labelDialogVisible=!0},handleLabelSelect:function(t){this.selectedLabels=t,this.labelDialogVisible=!1,this.selectedLabels.length?(t=this.selectedLabels.map((function(t){return t.id})),this.filterForm.label_ids=t.join(",")):this.filterForm.label_ids=""},removeLabel:function(t){this.selectedLabels.splice(t,1),this.selectedLabels.length?(t=this.selectedLabels.map((function(t){return t.id})),this.filterForm.label_ids=t.join(",")):this.filterForm.label_ids=""},openSelectTask:function(){this.taskDialogVisible=!0,this.getTaskList(),this.tempSelectedTasks=Object(s.a)(this.selectedTasks)},getTaskList:function(){var t=this;this.taskLoading=!0,Object(o.a)({page:1,limit:100}).then((function(e){t.taskList=e.data.list||[],t.taskLoading=!1,t.$nextTick((function(){var e=t.$refs.taskTable;e&&t.selectedTasks.length&&t.selectedTasks.forEach((function(i){var s=t.taskList.find((function(t){return t.id===i.id}));s&&e.toggleRowSelection(s,!0)}))}))})).catch((function(e){t.taskLoading=!1,t.$message.error(e.msg||"获取任务列表失败")}))},handleTaskSelectionChange:function(t){this.selectedTaskIds=t.map((function(t){return t.id})),this.tempSelectedTasks=t.map((function(t){return{id:t.id,title:t.title}}))},confirmSelectTasks:function(){var t;this.selectedTasks=Object(s.a)(this.tempSelectedTasks),this.taskDialogVisible=!1,this.selectedTasks.length?(t=this.selectedTasks.map((function(t){return t.id})),this.filterForm.task_ids=t.join(",")):this.filterForm.task_ids=""},removeTask:function(t){this.selectedTasks.splice(t,1),this.selectedTasks.length?(t=this.selectedTasks.map((function(t){return t.id})),this.filterForm.task_ids=t.join(",")):this.filterForm.task_ids=""},filterCustomers:function(){this.customerListQuery.page=1,this.customerListQuery.representative=this.filterForm.representative,this.customerListQuery.label_ids=this.filterForm.label_ids,this.customerListQuery.task_ids=this.filterForm.task_ids,this.customerListQuery.sign_id=this.currentSignId,this.getCustomerList()},resetFilter:function(){this.resetAddCustomerForm(),this.getCustomerList()},getCustomerList:function(){var t=this;this.customerLoading=!0,""==this.customerListQuery.sign_id&&(this.customerListQuery.sign_id=this.currentSignId),Object(r.i)(this.customerListQuery).then((function(e){t.filteredCustomers=e.data.list||[],t.customerLoading=!1})).catch((function(e){t.customerLoading=!1,t.$message.error(e.msg||"获取客户列表失败")}))},handleCheckboxChange:function(t){this.selectedCustomers=this.filteredCustomers.filter((function(e){return t.includes(e.id)}))},confirmAddCustomers:function(){var t,e=this;0===this.selectedCustomers.length?this.$message.warning("请选择要添加的客户"):(this.confirmLoading=!0,t=this.selectedCustomers.map((function(t){return t.id})),t={sign_id:this.currentSignId,customer_ids:t},Object(r.n)(t).then((function(t){e.$message.success("添加签到客户成功"),e.addCustomerDialogVisible=!1,e.confirmLoading=!1,e.getList()})).catch((function(t){e.$message.error(t.msg||"添加签到客户失败"),e.getList(),e.confirmLoading=!1})))},showAddedCustomers:function(t){var e=this;this.addedCustomersDialogVisible=!0,this.addedCustomersLoading=!0,this.addedCustomers=[],this.selectedAddedCustomerIds=[],this.currentAddedSignId=t.id,Object(r.k)({sign_id:t.id}).then((function(t){e.addedCustomers=t.data.list||[],e.selectedAddedCustomerIds=e.addedCustomers.map((function(t){return t.id})),e.addedCustomersLoading=!1})).catch((function(t){e.addedCustomersLoading=!1,e.$message.error(t.msg||"获取已添加客户列表失败")}))},confirmUpdateCustomers:function(){var t=this,e=(this.updateCustomersLoading=!0,{sign_id:this.currentAddedSignId,customer_ids:this.selectedAddedCustomerIds});Object(r.o)(e).then((function(){t.$message.success("更新签到客户成功"),t.addedCustomersDialogVisible=!1,t.updateCustomersLoading=!1,t.getList()})).catch((function(e){t.$message.error(e.msg||"更新签到客户失败"),t.updateCustomersLoading=!1}))}}},i("1dd5"),c=i("2877"),i=Object(c.a)(l,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never"}},[e("el-row",{attrs:{gutter:24,justify:"space-between"}},[e("el-col",{attrs:{span:24}},[e("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["admin-sign-add"],expression:"['admin-sign-add']"}],attrs:{type:"primary"},on:{click:t.add}},[t._v("添加签到")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:t.tableList,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"编号","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.id))])]}}])}),e("el-table-column",{attrs:{label:"标题","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.title))])]}}])}),e("el-table-column",{attrs:{label:"赠送积分","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.score))])]}}])}),e("el-table-column",{attrs:{label:"开始时间","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.start_time))])]}}])}),e("el-table-column",{attrs:{label:"结束时间","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",[t._v(t._s(i.row.end_time))])]}}])}),e("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.status)+"\n        ")]}}])}),e("el-table-column",{attrs:{label:"操作",fixed:"right",width:"150"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("a",{on:{click:function(e){return t.startSign(i.row)}}},[t._v("开始签到")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:function(e){return t.edit(i.row)}}},[t._v("编辑")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:function(e){return t.addCustrom(i.row)}}},[t._v("添加签到客户")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:function(e){return t.showAddedCustomers(i.row)}}},[t._v("已添加客户")]),e("el-divider",{attrs:{direction:"vertical"}}),e("a",{on:{click:function(e){return t.del(i.row,"删除签到活动",i.$index)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}):t._e()],1)],1),e("el-dialog",{attrs:{visible:t.addCustomerDialogVisible,title:"添加签到客户",width:"800px","close-on-click-modal":!1,"close-on-press-escape":!0},on:{"update:visible":function(e){t.addCustomerDialogVisible=e},closed:t.handleAddCustomerDialogClosed}},[e("div",{staticClass:"filter-container"},[e("el-form",{attrs:{model:t.filterForm,"label-width":"100px",inline:""}},[e("el-form-item",{attrs:{label:"客户代表："}},[e("el-select",{attrs:{placeholder:"请选择客户代表",clearable:""},model:{value:t.filterForm.representative,callback:function(e){t.$set(t.filterForm,"representative",e)},expression:"filterForm.representative"}},t._l(t.representativeOptions,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"客户标签："}},[e("div",{staticClass:"label-select",on:{click:t.openSelectLabel}},[t.selectedLabels.length?e("div",{staticClass:"selected-labels"},t._l(t.selectedLabels,(function(i,s){return e("el-tag",{key:s,staticClass:"mr10",attrs:{closable:""},on:{close:function(e){return e.stopPropagation(),t.removeLabel(s)}}},[t._v("\n                "+t._s(i.label_name)+"\n              ")])})),1):e("span",{staticClass:"placeholder"},[t._v("选择客户标签")]),e("i",{staticClass:"el-icon-arrow-down"})])]),e("el-form-item",{attrs:{label:"已参与任务："}},[e("div",{staticClass:"task-select",on:{click:t.openSelectTask}},[t.selectedTasks.length?e("div",{staticClass:"selected-tasks"},t._l(t.selectedTasks,(function(i,s){return e("el-tag",{key:s,staticClass:"mr10",attrs:{closable:""},on:{close:function(e){return e.stopPropagation(),t.removeTask(s)}}},[t._v("\n                "+t._s(i.title)+"\n              ")])})),1):e("span",{staticClass:"placeholder"},[t._v("选择已参与任务")]),e("i",{staticClass:"el-icon-arrow-down"})])]),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.filterCustomers}},[t._v("筛选")]),e("el-button",{on:{click:t.resetFilter}},[t._v("重置")])],1)],1)],1),e("div",{staticClass:"customer-list"},[e("el-checkbox-group",{on:{change:t.handleCheckboxChange},model:{value:t.selectedCustomerIds,callback:function(e){t.selectedCustomerIds=e},expression:"selectedCustomerIds"}},[e("div",{staticClass:"customer-grid"},t._l(t.filteredCustomers,(function(i){return e("div",{key:i.id,staticClass:"customer-item"},[e("el-checkbox",{attrs:{label:i.id}},[t._v(t._s(i.name))])],1)})),0)])],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.addCustomerDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary",loading:t.confirmLoading},on:{click:t.confirmAddCustomers}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{visible:t.taskDialogVisible,title:"选择任务",width:"600px","append-to-body":""},on:{"update:visible":function(e){t.taskDialogVisible=e}}},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.taskLoading,expression:"taskLoading"}],ref:"taskTable",staticStyle:{width:"100%"},attrs:{data:t.taskList,border:"",height:"400px"},on:{"selection-change":t.handleTaskSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"55"}}),e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"title",label:"任务名称"}})],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.taskDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectTasks}},[t._v("确 定")])],1)],1),e("el-dialog",{attrs:{visible:t.labelDialogVisible,title:"选择客户标签",width:"600px","append-to-body":""},on:{"update:visible":function(e){t.labelDialogVisible=e}}},[e("userLabel",{attrs:{uid:0,only_get:!0,selectDataLabel:t.selectedLabels},on:{activeData:t.handleLabelSelect,close:function(e){t.labelDialogVisible=!1}}})],1),e("el-dialog",{attrs:{visible:t.addedCustomersDialogVisible,title:"已添加客户列表",width:"600px","close-on-click-modal":!1},on:{"update:visible":function(e){t.addedCustomersDialogVisible=e}}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.addedCustomersLoading,expression:"addedCustomersLoading"}],staticClass:"added-customers-list"},[e("el-checkbox-group",{model:{value:t.selectedAddedCustomerIds,callback:function(e){t.selectedAddedCustomerIds=e},expression:"selectedAddedCustomerIds"}},[e("div",{staticClass:"customer-grid"},t._l(t.addedCustomers,(function(i){return e("div",{key:i.id,staticClass:"customer-item"},[e("el-checkbox",{attrs:{label:i.id}},[t._v(t._s(i.name))])],1)})),0)])],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.addedCustomersDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary",loading:t.updateCustomersLoading},on:{click:t.confirmUpdateCustomers}},[t._v("确 定")])],1)]),e("el-dialog",{staticClass:"sign-dialog",attrs:{visible:t.signDialogVisible,title:"签到活动："+t.currentSign.title,width:"100%",fullscreen:"","show-close":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.signDialogVisible=e},closed:t.handleSignDialogClosed}},[e("div",{staticClass:"sign-container"},[e("div",{staticClass:"sign-header"},[e("div",{staticClass:"sign-time-info"},[e("div",{staticClass:"sign-time-item"},[e("span",{staticClass:"time-label"},[t._v("开始时间：")]),e("span",{staticClass:"time-value"},[t._v(t._s(t.currentSign.start_time_formatted))])]),e("div",{staticClass:"sign-time-item"},[e("span",{staticClass:"time-label"},[t._v("结束时间：")]),e("span",{staticClass:"time-value"},[t._v(t._s(t.currentSign.end_time_formatted))])]),e("div",{staticClass:"sign-time-item"},[e("span",{staticClass:"time-label"},[t._v("剩余时间：")]),e("span",{staticClass:"time-value countdown"},[t._v(t._s(t.remainingTime))])])])]),e("div",{staticClass:"sign-input-container"},[e("el-input",{ref:"cardInput",staticClass:"sign-input",attrs:{placeholder:"请刷卡或输入会员卡号",autofocus:""},on:{input:t.handleCardInput,blur:t.handleInputBlur},model:{value:t.cardNumber,callback:function(e){t.cardNumber=e},expression:"cardNumber"}}),e("el-button",{attrs:{type:"primary",disabled:!t.cardNumber},on:{click:t.submitSign}},[t._v("确认签到")])],1),e("div",{staticClass:"sign-status"},[e("div",{staticClass:"sign-status-item"},[e("h4",[t._v("已签到人数："+t._s(t.signedMembers.length))])]),e("div",{staticClass:"sign-status-item"},[e("h4",[t._v("待签到人数："+t._s(t.unsignedMembers.length))])])]),e("div",{staticClass:"sign-lists"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"sign-list-container"},[e("h4",[t._v("已签到人员列表")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.signedMembers,height:"calc(100vh - 400px)",border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"name",label:"姓名"}}),e("el-table-column",{attrs:{prop:"cardNumber",label:"卡号"}}),e("el-table-column",{attrs:{prop:"add_time",label:"签到时间"}})],1)],1)]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"sign-list-container"},[e("h4",[t._v("待签到人员列表")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.unsignedMembers,height:"calc(100vh - 400px)",border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"name",label:"姓名"}}),e("el-table-column",{attrs:{prop:"cardNumber",label:"卡号"}}),e("el-table-column",{attrs:{prop:"tel",label:"手机号"}})],1)],1)])],1)],1)])])],1)}),[],!1,null,"20a4c2cf",null);e.default=i.exports},b199:function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));var s=i("6b6c");function n(t){return Object(s.a)({url:"task/getList",method:"get",params:t})}},b644:function(t,e,i){"use strict";i("80d0")},f6b0:function(t,e,i){"use strict";i.d(e,"f",(function(){return n})),i.d(e,"e",(function(){return a})),i.d(e,"d",(function(){return r})),i.d(e,"m",(function(){return o})),i.d(e,"a",(function(){return l})),i.d(e,"b",(function(){return c})),i.d(e,"l",(function(){return d})),i.d(e,"h",(function(){return u})),i.d(e,"g",(function(){return m})),i.d(e,"c",(function(){return g})),i.d(e,"i",(function(){return f})),i.d(e,"j",(function(){return h})),i.d(e,"n",(function(){return b})),i.d(e,"o",(function(){return p})),i.d(e,"k",(function(){return v}));var s=i("6b6c");function n(t){return Object(s.a)({url:"customer/getList",method:"get",params:t})}function a(t){return Object(s.a)({url:"customer/getCustomerLabelList",method:"get",params:t})}function r(t){return Object(s.a)({url:"customer/label/add",method:"get",params:{id:t}})}function o(){return Object(s.a)({url:"customer/level/list",method:"get"})}function l(t){return Object(s.a)({url:"customer/level/create/"+t,method:"get"})}function c(t){return Object(s.a)({url:"customer/info/".concat(t),method:"get"})}function d(t){return Object(s.a)({url:"customer/one_info",method:"get",params:t})}function u(t){return Object(s.a)({url:"customer/point_record",method:"get",params:t})}function m(t){return Object(s.a)({url:"customer/point/card_info",method:"get",params:{card_number:t}})}function g(t){return Object(s.a)({url:"/customer/editPoint/"+t,method:"get"})}function f(t){return Object(s.a)({url:"customer/signCustomerList",method:"get",params:t})}function h(t){return Object(s.a)({url:"customer/getSignUserList",method:"get",params:t})}function b(t){return Object(s.a)({url:"customer/signCustomerSave",method:"post",data:t})}function p(t){return Object(s.a)({url:"customer/signCustomerUpdate",method:"post",data:t})}function v(t){return Object(s.a)({url:"customer/getSignCustomer",method:"get",params:t})}}}]);