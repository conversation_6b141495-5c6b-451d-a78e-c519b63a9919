(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-270dfe95"],{"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},"841c":function(t,e,n){"use strict";var i=n("c65b"),o=n("d784"),r=n("825a"),a=n("7234"),s=n("1d80"),u=n("129f"),l=n("577e"),c=n("dc4a"),d=n("14c3");o("search",(function(t,e,n){return[function(e){var n=s(this),o=a(e)?void 0:c(e,t);return o?i(o,e,n):new RegExp(e)[t](l(n))},function(t){var i=r(this),o=(t=l(t),n(e,i,t));return o.done?o.value:(o=i.lastIndex,u(o,0)||(i.lastIndex=0),t=d(i,t),u(i.lastIndex,o)||(i.lastIndex=o),null===t?-1:t.index)}]}))},"9ce6":function(t,e,n){},a41a:function(t,e,n){"use strict";n.r(e),n("ac1f"),n("841c");var i=n("f6b0"),o={name:"customer_point_type",components:{pagination:n("333d").a},data:function(){return{loading:!1,tableList:[],total:0,formValidate:{page:1,limit:15,keyword:"",status:""},dialogVisible:!1,dialogTitle:"添加积分项",isEdit:!1,editId:null,submitLoading:!1,pointTypeForm:{type_name:"",point:0,status:1},rules:{type_name:[{required:!0,message:"请输入类型名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],point:[{required:!0,message:"请输入积分值",trigger:"blur"},{type:"number",min:-99999,max:99999,message:"积分值必须在 -99999 到 99999 之间",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(i.m)(this.formValidate).then((function(e){t.tableList=e.data.list||[],t.total=e.data.count||0,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.msg||"获取积分项列表失败")}))},search:function(){this.formValidate.page=1,this.getList()},reset:function(){this.formValidate.keyword="",this.formValidate.status="",this.formValidate.page=1,this.getList()},add:function(){this.dialogTitle="添加积分项",this.isEdit=!1,this.editId=null,this.dialogVisible=!0},edit:function(t){this.dialogTitle="编辑积分项",this.isEdit=!0,this.editId=t.id,this.pointTypeForm={type_name:t.type_name,point:t.point,status:t.status},this.dialogVisible=!0},del:function(t){var e=this;this.$confirm("确定要删除这个积分项吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(i.e)(t.id).then((function(t){e.$message.success("删除成功"),e.getList()})).catch((function(t){e.$message.error(t.msg||"删除失败")}))})).catch((function(){}))},changeStatus:function(t){var e=this;Object(i.b)(t.id,t.status).then((function(t){e.$message.success("状态修改成功")})).catch((function(n){t.status=1===t.status?0:1,e.$message.error(n.msg||"状态修改失败")}))},submitForm:function(){var t=this;this.$refs.pointTypeForm.validate((function(e){e&&(t.submitLoading=!0,(t.isEdit?Object(i.g)(t.editId,t.pointTypeForm):Object(i.a)(t.pointTypeForm)).then((function(e){t.$message.success(t.isEdit?"编辑成功":"添加成功"),t.dialogVisible=!1,t.getList(),t.submitLoading=!1})).catch((function(e){t.$message.error(e.msg||(t.isEdit?"编辑失败":"添加失败")),t.submitLoading=!1})))}))},resetForm:function(){this.pointTypeForm={type_name:"",point:0,status:1},this.$refs.pointTypeForm&&this.$refs.pointTypeForm.resetFields()}}};n("b31a"),n=n("2877"),n=Object(n.a)(o,(function(){var t=this,e=t._self._c;return e("div",[e("el-card",{staticClass:"ivu-mt",attrs:{bordered:!1,shadow:"never"}},[e("div",{staticClass:"tabform"},[e("el-form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":"80px",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"类型名称："}},[e("el-input",{staticClass:"form_content_width",attrs:{clearable:"",placeholder:"请输入类型名称"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1),e("el-form-item",{attrs:{label:"状态："}},[e("el-select",{staticClass:"form_content_width",attrs:{placeholder:"请选择状态",clearable:""},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"启用",value:1}}),e("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")]),e("el-button",{on:{click:t.reset}},[t._v("重置")])],1)],1)],1),e("el-row",{attrs:{gutter:24,justify:"space-between"}},[e("el-col",{attrs:{span:24}},[e("el-button",{attrs:{type:"primary"},on:{click:t.add}},[t._v("添加积分项")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticClass:"mt16",attrs:{data:t.tableList,"highlight-current-row":"","empty-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"}},[e("el-table-column",{attrs:{label:"编号","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.id))])]}}])}),e("el-table-column",{attrs:{label:"类型名称","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(n.row.type_name))])]}}])}),e("el-table-column",{attrs:{label:"积分值","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",{class:["point-value",n.row.point<0?"negative":"positive"]},[t._v("\n            "+t._s(0<n.row.point?"+":"")+t._s(n.row.point)+"\n          ")])]}}])}),e("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},on:{change:function(e){return t.changeStatus(n.row)}},model:{value:n.row.status,callback:function(e){t.$set(n.row,"status",e)},expression:"scope.row.status"}})]}}])}),e("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.edit(n.row)}}},[t._v("编辑")]),e("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.del(n.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"acea-row row-right page"},[t.total?e("pagination",{attrs:{total:t.total,page:t.formValidate.page,limit:t.formValidate.limit},on:{"update:page":function(e){return t.$set(t.formValidate,"page",e)},"update:limit":function(e){return t.$set(t.formValidate,"limit",e)},pagination:t.getList}}):t._e()],1)],1),e("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e},close:t.resetForm}},[e("el-form",{ref:"pointTypeForm",attrs:{model:t.pointTypeForm,rules:t.rules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"类型名称",prop:"type_name"}},[e("el-input",{attrs:{placeholder:"请输入类型名称",maxlength:"50","show-word-limit":""},model:{value:t.pointTypeForm.type_name,callback:function(e){t.$set(t.pointTypeForm,"type_name",e)},expression:"pointTypeForm.type_name"}})],1),e("el-form-item",{attrs:{label:"积分值",prop:"point"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:-99999,max:99999,placeholder:"请输入积分值（负数代表扣减）"},model:{value:t.pointTypeForm.point,callback:function(e){t.$set(t.pointTypeForm,"point",e)},expression:"pointTypeForm.point"}}),e("div",{staticClass:"form-tip"},[t._v("提示：正数代表增加积分，负数代表扣减积分")])],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.pointTypeForm.status,callback:function(e){t.$set(t.pointTypeForm,"status",e)},expression:"pointTypeForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.submitLoading},on:{click:t.submitForm}},[t._v("确定")])],1)],1)],1)}),[],!1,null,"d26492a0",null);e.default=n.exports},b31a:function(t,e,n){"use strict";n("9ce6")},f6b0:function(t,e,n){"use strict";n.d(e,"j",(function(){return o})),n.d(e,"i",(function(){return r})),n.d(e,"h",(function(){return a})),n.d(e,"r",(function(){return s})),n.d(e,"c",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"q",(function(){return c})),n.d(e,"l",(function(){return d})),n.d(e,"k",(function(){return m})),n.d(e,"v",(function(){return p})),n.d(e,"d",(function(){return f})),n.d(e,"m",(function(){return b})),n.d(e,"a",(function(){return g})),n.d(e,"g",(function(){return h})),n.d(e,"e",(function(){return v})),n.d(e,"b",(function(){return y})),n.d(e,"n",(function(){return _})),n.d(e,"o",(function(){return w})),n.d(e,"t",(function(){return j})),n.d(e,"u",(function(){return x})),n.d(e,"p",(function(){return O})),n.d(e,"s",(function(){return k})),n("99af");var i=n("6b6c");function o(t){return Object(i.a)({url:"customer/getList",method:"get",params:t})}function r(t){return Object(i.a)({url:"customer/getCustomerLabelList",method:"get",params:t})}function a(t){return Object(i.a)({url:"customer/label/add",method:"get",params:{id:t}})}function s(){return Object(i.a)({url:"customer/level/list",method:"get"})}function u(t){return Object(i.a)({url:"customer/level/create/"+t,method:"get"})}function l(t){return Object(i.a)({url:"customer/info/".concat(t),method:"get"})}function c(t){return Object(i.a)({url:"customer/one_info",method:"get",params:t})}function d(t){return Object(i.a)({url:"customer/point_record",method:"get",params:t})}function m(t){return Object(i.a)({url:"customer/point/card_info",method:"get",params:{card_number:t}})}function p(t){return Object(i.a)({url:"customer/transfer",method:"post",data:t})}function f(t){return Object(i.a)({url:"customer/delete/".concat(t),method:"DELETE"})}function b(t){return Object(i.a)({url:"customer/point_type/list",method:"get",params:t})}function g(t){return Object(i.a)({url:"customer/point_type/add",method:"post",data:t})}function h(t,e){return Object(i.a)({url:"customer/point_type/edit/".concat(t),method:"post",data:e})}function v(t){return Object(i.a)({url:"customer/point_type/delete/".concat(t),method:"DELETE"})}function y(t,e){return Object(i.a)({url:"customer/point_type/status/".concat(t,"/").concat(e),method:"put"})}function _(t){return Object(i.a)({url:"customer/signCustomerList",method:"get",params:t})}function w(t){return Object(i.a)({url:"customer/getSignUserList",method:"get",params:t})}function j(t){return Object(i.a)({url:"customer/signCustomerSave",method:"post",data:t})}function x(t){return Object(i.a)({url:"customer/signCustomerUpdate",method:"post",data:t})}function O(t){return Object(i.a)({url:"customer/getSignCustomer",method:"get",params:t})}function k(t){return Object(i.a)({url:"customer/operateCustomerPoints",method:"post",data:t})}}}]);